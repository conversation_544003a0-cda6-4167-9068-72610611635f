@extends('admin.layouts.app')

@section('title', 'Penugasan Guru')

@section('content')
    @include('admin.components.page-title', ['title' => 'Penugasan Guru', 'breadcrumb' => 'Penuga<PERSON> Guru'])

    <div class="row">
        <div class="col-lg-12">
            <div class="card" id="tasksList">
                <!-- Start Header -->
                <div class="card-header border-0">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">Pen<PERSON><PERSON>
                            <span class="badge bg-primary-subtle text-primary ms-2" id="total-teacher-assignments">0</span>
                        </h5>
                    </div>
                </div>
                <!-- End Header -->

                <!-- Start Filter Section -->
                <div class="card-body border border-dashed border-end-0 border-start-0">
                    <form id="filter-form" class="row g-3">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filter_teacher" class="form-label">Guru</label>
                                <select class="form-select" data-choices name="teacher_id" id="filter_teacher">
                                    <option value="">Semua <PERSON></option>
                                    @foreach ($teachers as $teacher)
                                        <option value="{{ $teacher->id }}">{{ $teacher->user->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filter_subject" class="form-label">Mata Pelajaran</label>
                                <select class="form-select" data-choices name="subject_id" id="filter_subject">
                                    <option value="">Semua Mata Pelajaran</option>
                                    @foreach ($subjects as $subject)
                                        <option value="{{ $subject->id }}">{{ $subject->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filter_classroom" class="form-label">Kelas</label>
                                <select class="form-select" data-choices name="classroom_id" id="filter_classroom">
                                    <option value="">Semua Kelas</option>
                                    @foreach ($classrooms as $classroom)
                                        <option value="{{ $classroom->id }}">{{ $classroom->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filter_academic_year" class="form-label">Tahun Akademik</label>
                                <select class="form-select" data-choices name="academic_year_id" id="filter_academic_year">
                                    <option value="">Semua Tahun Akademik</option>
                                    @foreach ($academicYears as $academicYear)
                                        <option value="{{ $academicYear->id }}">{{ $academicYear->name }} - {{ $academicYear->semester->label() }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <!-- Search Input -->
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="search-input" class="form-label">Cari</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Cari Penugasan Guru" id="search-input" name="search">
                                    <button class="btn btn-primary" type="button" id="search-button">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->

                <!-- Start Table Section -->
                <div class="card-body">
                    <div class="table-responsive table-card">
                        <table id="teacher-assignments-table" class="table align-middle table-nowrap" style="width: 100%;">
                            <thead class="table-light text-muted">
                                <tr>
                                    <th scope="col" style="width: 65px;">
                                        No
                                    </th>
                                    <th>Guru</th>
                                    <th>Mata Pelajaran</th>
                                    <th>Kelas</th>
                                    <th>Tahun Akademik</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->

            </div>
            <!--end card-->
        </div>
        <!--end col-->
    </div>
    <!--end row-->
@endsection

@include('admin.partials.plugins._jquery')
@include('admin.partials.plugins._datatables')

@push('scripts')
    <script>
        $(document).ready(function() {
            const table = $('#teacher-assignments-table').DataTable({
                lengthChange: false,
                searching: false,
                ordering: false,
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('admin.teacher_assignments.index') }}",
                    data: function(d) {
                        d.columns[1].search.value = $('#filter_teacher').val(); // teacher
                        d.columns[2].search.value = $('#filter_subject').val(); // subject
                        d.columns[3].search.value = $('#filter_classroom').val(); // classroom
                        d.columns[4].search.value = $('#filter_academic_year').val(); // academic_year
                        d.search.value = $('#search-input').val(); // search
                    },
                    complete: response => {
                        const totalRecords = response.responseJSON?.recordsTotal || 0;
                        $('#total-teacher-assignments').text(totalRecords);
                    },
                    error: xhr => {
                        const message = xhr.responseJSON?.message || 'Gagal memuat data penugasan guru.';
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'teacher_name',
                        name: 'teacher_name'
                    },
                    {
                        data: 'subject_name',
                        name: 'subject_name'
                    },
                    {
                        data: 'classroom_name',
                        name: 'classroom_name'
                    },
                    {
                        data: 'academic_year_name',
                        name: 'academic_year_name'
                    },
                    {
                        data: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: "Cari...",
                    lengthMenu: "Tampilkan _MENU_ data",
                    zeroRecords: "Data tidak ditemukan",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "(disaring dari _MAX_ total data)",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya",
                    }
                },
            });

            // Reload tabel saat filter berubah
            $('#filter_teacher, #filter_subject, #filter_classroom, #filter_academic_year').change(function() {
                table.draw();
            });

            // Search button
            $('#search-button').click(function() {
                table.draw();
            });

            // Menyesuaikan kolom tabel saat sidebar di-toggle
            $('#topnav-hamburger-icon').click(() => {
                setTimeout(() => {
                    table.columns.adjust().draw();
                }, 300);
            });

            // Delete button
            table.on('click', '.btn-delete-item', function() {
                const url = $(this).data('url');
                Swal.fire({
                    title: 'Apakah Anda yakin ingin menghapus data ini?',
                    text: 'Data yang dihapus tidak dapat dikembalikan!',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Ya, hapus!',
                    cancelButtonText: 'Batal',
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: url,
                            type: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                table.draw();
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: response.message || 'Data berhasil dihapus!',
                                    icon: 'success',
                                    showConfirmButton: true,
                                });
                            },
                            error: function(xhr, status, error) {
                                Swal.fire({
                                    title: 'Gagal!',
                                    text: xhr.responseJSON?.message || 'Data gagal dihapus!',
                                    showConfirmButton: true,
                                    icon: 'error',
                                });
                            }
                        });
                    }
                });
            });
        });
    </script>
@endpush
