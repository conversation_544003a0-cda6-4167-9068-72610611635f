@extends('admin.layouts.app')

@section('title', 'Tambah Jadwal Pelajaran')

@section('content')
    @include('admin.components.page-title', ['title' => 'Tambah Jadwal Pelajaran', 'breadcrumb' => 'Tambah Jadwal Pelajaran'])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Tambah Jadwal Pelajaran</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.class_schedules.store') }}" method="POST">
                        @csrf
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="teacher_assignment_id" class="form-label">Penuga<PERSON> Guru <span class="text-danger">*</span></label>
                                    <select class="form-select" data-choices name="teacher_assignment_id" id="teacher_assignment_id" required>
                                        <option value=""><PERSON><PERSON><PERSON></option>
                                        @foreach ($teacherAssignments as $assignment)
                                            <option value="{{ $assignment->id }}">
                                                {{ $assignment->teacher->user->name }} -
                                                {{ $assignment->subject->name }} -
                                                {{ $assignment->classroom->name }} -
                                                {{ $assignment->academicYear->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('teacher_assignment_id')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="lesson_hour_id" class="form-label">Jam Pelajaran <span class="text-danger">*</span></label>
                                    <select class="form-select" data-choices name="lesson_hour_id" id="lesson_hour_id" required>
                                        <option value="">Pilih Jam Pelajaran</option>
                                        @foreach ($lessonHours as $lessonHour)
                                            <option value="{{ $lessonHour->id }}">
                                                {{ $lessonHour->name }} ({{ $lessonHour->start_time->format('H:i') }} - {{ $lessonHour->end_time->format('H:i') }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('lesson_hour_id')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="day_of_week" class="form-label">Hari <span class="text-danger">*</span></label>
                                    <select class="form-select" data-choices name="day_of_week" id="day_of_week" required>
                                        <option value="">Pilih Hari</option>
                                        @foreach ($daysOfWeek as $value => $label)
                                            <option value="{{ $value }}">{{ $label }}</option>
                                        @endforeach
                                    </select>
                                    @error('day_of_week')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="schedule_status" class="form-label">Status Jadwal <span class="text-danger">*</span></label>
                                    <select class="form-select" data-choices name="schedule_status" id="schedule_status" required>
                                        <option value="">Pilih Status</option>
                                        @foreach ($scheduleStatuses as $value => $label)
                                            <option value="{{ $value }}">{{ $label }}</option>
                                        @endforeach
                                    </select>
                                    @error('schedule_status')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.class_schedules.index') }}" class="btn btn-soft-secondary">Batal</a>
                                    <button type="submit" class="btn btn-primary">Simpan</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
