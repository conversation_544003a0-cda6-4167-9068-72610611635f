<?php

namespace App\Http\Controllers\Api;

use App\Enums\DayOfWeekEnum;
use App\Enums\ScheduleStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Requests\ClassScheduleRequests\ClassScheduleApiRequest;
use App\Models\AcademicYear;
use App\Models\Classroom;
use App\Models\ClassSchedule;
use App\Models\LessonHour;
use App\Models\TeacherAssignment;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ClassScheduleApiController extends Controller
{
    /**
     * Get schedules by classroom and academic year.
     */
    public function getSchedules(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'classroom_id' => 'required|exists:classrooms,id',
            'academic_year_id' => 'required|exists:academic_years,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $classroom = Classroom::findOrFail($request->classroom_id);
            $academicYear = AcademicYear::findOrFail($request->academic_year_id);

            // Get lesson hours for the classroom or shift, or global if none exist
            $lessonHours = LessonHour::where(function ($query) use ($classroom) {
                $query->where('classroom_id', $classroom->id)
                    ->orWhere(function ($q) use ($classroom) {
                        if ($classroom->shift_id) {
                            $q->whereNull('classroom_id')
                                ->where('shift_id', $classroom->shift_id);
                        }
                    });
            })
                ->orderBy('sequence')
                ->get();

            // If no specific lesson hours found, use global lesson hours
            if ($lessonHours->isEmpty()) {
                $lessonHours = LessonHour::whereNull('classroom_id')
                    ->whereNull('shift_id')
                    ->orderBy('sequence')
                    ->get();
            }

            // Get teacher assignments for the classroom and academic year
            $teacherAssignments = TeacherAssignment::with(['teacher.user', 'subject'])
                ->where('classroom_id', $classroom->id)
                ->where('academic_year_id', $academicYear->id)
                ->get();

            // Get schedules for the classroom and academic year
            $schedules = ClassSchedule::with(['teacherAssignment.teacher.user', 'teacherAssignment.subject', 'lessonHour'])
                ->whereHas('teacherAssignment', function ($query) use ($classroom, $academicYear) {
                    $query->where('classroom_id', $classroom->id)
                        ->where('academic_year_id', $academicYear->id);
                })
                ->get()
                ->map(function ($schedule) {
                    return [
                        'id' => $schedule->id,
                        'teacher_assignment_id' => $schedule->teacher_assignment_id,
                        'lesson_hour_id' => $schedule->lesson_hour_id,
                        'day_of_week' => $schedule->day_of_week->value,
                        'day_label' => $schedule->day_of_week->label(),
                        'schedule_status' => $schedule->schedule_status->value,
                        'status_label' => $schedule->schedule_status->label(),
                        'status_color' => $schedule->schedule_status->color(),
                        'teacher_name' => $schedule->teacherAssignment->teacher->user->name,
                        'subject_name' => $schedule->teacherAssignment->subject->name,
                        'start_time' => $schedule->lessonHour->start_time->format('H:i'),
                        'end_time' => $schedule->lessonHour->end_time->format('H:i'),
                        'sequence' => $schedule->lessonHour->sequence,
                    ];
                });

            // Format days of week for frontend
            $daysOfWeek = collect(DayOfWeekEnum::cases())->map(function ($day) {
                return [
                    'value' => $day->value,
                    'label' => $day->label(),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'classroom' => $classroom,
                    'academicYear' => $academicYear,
                    'lessonHours' => $lessonHours,
                    'teacherAssignments' => $teacherAssignments->map(function ($assignment) {
                        return [
                            'id' => $assignment->id,
                            'teacher_id' => $assignment->teacher_id,
                            'subject_id' => $assignment->subject_id,
                            'classroom_id' => $assignment->classroom_id,
                            'academic_year_id' => $assignment->academic_year_id,
                            'teacher_name' => $assignment->teacher->user->name,
                            'subject_name' => $assignment->subject->name,
                        ];
                    }),
                    'schedules' => $schedules,
                    'daysOfWeek' => $daysOfWeek,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil data jadwal: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created schedule in storage.
     */
    public function store(ClassScheduleApiRequest $request): JsonResponse
    {
        $data = $request->validated();

        // Set default status if not provided
        if (!isset($data['schedule_status'])) {
            $data['schedule_status'] = 'active';
        }

        try {
            // Check if schedule with same time slot already exists
            $scheduleExists = ClassSchedule::where([
                'lesson_hour_id' => $data['lesson_hour_id'],
                'day_of_week' => $data['day_of_week'],
            ])->exists();

            if ($scheduleExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Jadwal dengan slot waktu yang sama sudah ada.'
                ], 422);
            }

            // Create new schedule using transaction
            $schedule = DB::transaction(function () use ($data) {
                return ClassSchedule::create($data);
            });

            // Load relationships for response
            $schedule->load(['teacherAssignment.teacher.user', 'teacherAssignment.subject', 'lessonHour']);

            return response()->json([
                'success' => true,
                'message' => 'Jadwal pelajaran berhasil ditambahkan.',
                'data' => [
                    'id' => $schedule->id,
                    'teacher_assignment_id' => $schedule->teacher_assignment_id,
                    'lesson_hour_id' => $schedule->lesson_hour_id,
                    'day_of_week' => $schedule->day_of_week->value,
                    'day_label' => $schedule->day_of_week->label(),
                    'schedule_status' => $schedule->schedule_status->value,
                    'status_label' => $schedule->schedule_status->label(),
                    'status_color' => $schedule->schedule_status->color(),
                    'teacher_name' => $schedule->teacherAssignment->teacher->user->name,
                    'subject_name' => $schedule->teacherAssignment->subject->name,
                    'start_time' => $schedule->lessonHour->start_time->format('H:i'),
                    'end_time' => $schedule->lessonHour->end_time->format('H:i'),
                    'sequence' => $schedule->lessonHour->sequence,
                ]
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menambahkan jadwal pelajaran: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified schedule in storage.
     */
    public function update(ClassScheduleApiRequest $request, string $id): JsonResponse
    {
        $data = $request->validated();

        // Set default status if not provided
        if (!isset($data['schedule_status'])) {
            $data['schedule_status'] = 'active';
        }

        try {
            // Find the schedule
            $schedule = ClassSchedule::findOrFail($id);

            // Check if schedule with same time slot already exists (excluding current schedule)
            $scheduleExists = ClassSchedule::where([
                'lesson_hour_id' => $data['lesson_hour_id'],
                'day_of_week' => $data['day_of_week'],
            ])
                ->where('id', '!=', $id)
                ->exists();

            if ($scheduleExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Jadwal dengan slot waktu yang sama sudah ada.'
                ], 422);
            }

            // Update schedule using transaction
            DB::transaction(function () use ($schedule, $data) {
                $schedule->update($data);
            });

            // Reload relationships for response
            $schedule->load(['teacherAssignment.teacher.user', 'teacherAssignment.subject', 'lessonHour']);

            return response()->json([
                'success' => true,
                'message' => 'Jadwal pelajaran berhasil diperbarui.',
                'data' => [
                    'id' => $schedule->id,
                    'teacher_assignment_id' => $schedule->teacher_assignment_id,
                    'lesson_hour_id' => $schedule->lesson_hour_id,
                    'day_of_week' => $schedule->day_of_week->value,
                    'day_label' => $schedule->day_of_week->label(),
                    'schedule_status' => $schedule->schedule_status->value,
                    'status_label' => $schedule->schedule_status->label(),
                    'status_color' => $schedule->schedule_status->color(),
                    'teacher_name' => $schedule->teacherAssignment->teacher->user->name,
                    'subject_name' => $schedule->teacherAssignment->subject->name,
                    'start_time' => $schedule->lessonHour->start_time->format('H:i'),
                    'end_time' => $schedule->lessonHour->end_time->format('H:i'),
                    'sequence' => $schedule->lessonHour->sequence,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memperbarui jadwal pelajaran: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified schedule from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $schedule = ClassSchedule::findOrFail($id);
            $schedule->delete();

            return response()->json([
                'success' => true,
                'message' => 'Jadwal pelajaran berhasil dihapus.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus jadwal pelajaran: ' . $e->getMessage()
            ], 500);
        }
    }
}
