<ul class="list-inline hstack gap-2 mb-0 text-center">
    <li class="list-inline-item" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-placement="top" title="Edit">
        <a href="javascript:void(0);" class="text-muted d-inline-block btn-edit-assignment"
           data-id="{{ $row->id }}"
           data-teacher-id="{{ $row->teacher_id }}"
           data-subject-id="{{ $row->subject_id }}"
           data-bs-toggle="modal" data-bs-target="#editTeacherAssignmentModal">
            <i class="ri-edit-2-line fs-16"></i>
        </a>
    </li>
    <li class="list-inline-item" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-placement="top" title="Delete">
        <a href="javascript:void(0);" class="text-danger d-inline-block btn-delete-assignment"
           data-id="{{ $row->id }}"
           data-url="{{ route('admin.classrooms.teacher-assignments.destroy', ['classroom' => $classroom->id, 'assignment' => $row->id]) }}">
            <i class="ri-delete-bin-line fs-16"></i>
        </a>
    </li>
</ul>
