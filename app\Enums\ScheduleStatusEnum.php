<?php

namespace App\Enums;

enum ScheduleStatusEnum: string
{
    case Active = 'active';
    case Cancelled = 'cancelled';
    case Completed = 'completed';
    case Pending = 'pending';

    public function label(): string
    {
        return match ($this) {
            self::Active => 'Aktif',
            self::Cancelled => 'Dibatalkan',
            self::Completed => 'Selesai',
            self::Pending => 'Menunggu',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::Active => 'success',
            self::Cancelled => 'danger',
            self::Completed => 'info',
            self::Pending => 'warning',
        };
    }

    public static function options(): array
    {
        return collect(self::cases())->mapWithKeys(fn($case) => [
            $case->value => $case->label()
        ])->toArray();
    }
}
