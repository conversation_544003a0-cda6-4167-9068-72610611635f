/**
 * Class Schedule Management Vue.js Application
 */

// Create the Vue.js app
const { createApp, ref, onMounted, computed, watch } = Vue;

const ScheduleApp = {
    setup() {
        // State variables
        const loading = ref(true);
        const error = ref(null);
        const classroom = ref(null);
        const academicYear = ref(null);
        const lessonHours = ref([]);
        const teacherAssignments = ref([]);
        const schedules = ref({});
        const days = ref({});
        const activeTab = ref("schedule");
        const submitting = ref(false);
        const draggedSchedule = ref(null);
        const scheduleModalMode = ref("create");
        const currentSchedule = ref(null);
        const scheduleForm = ref({
            teacher_assignment_id: "",
            lesson_hour_id: "",
            day_of_week: "",
        });
        const scheduleErrors = ref({});
        const scheduleCount = ref(0);
        const scheduleFormChanged = ref(false);
        const subjectColors = ref({});
        const colorPalette = [
            "#3498db",
            "#2ecc71",
            "#9b59b6",
            "#e74c3c",
            "#f39c12",
            "#1abc9c",
            "#d35400",
            "#34495e",
            "#16a085",
            "#27ae60",
            "#8e44ad",
            "#f1c40f",
            "#e67e22",
            "#c0392b",
            "#7f8c8d",
        ];

        // Get DOM element data
        const appElement = document.getElementById("schedule-app");
        const classroomId = appElement.dataset.classroomId;
        const academicYearId = appElement.dataset.academicYearId;

        // Get CSRF token
        const csrfToken = document
            .querySelector('meta[name="csrf-token"]')
            .getAttribute("content");

        // Format time to display in HH:MM format
        const formatTime = (timeString) => {
            if (!timeString) return "";
            if (timeString.includes("T")) {
                const date = new Date(timeString);
                return date.toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                });
            }
            return timeString;
        };

        // Fetch data from API
        const fetchData = async () => {
            try {
                loading.value = true;
                const response = await fetch(
                    `/admin/api/class_schedules?classroom_id=${classroomId}&academic_year_id=${academicYearId}`,
                    {
                        headers: {
                            Accept: "application/json",
                            "X-CSRF-TOKEN": csrfToken,
                        },
                    }
                );

                const result = await response.json();

                if (result.success) {
                    classroom.value = result.data.classroom;
                    academicYear.value = result.data.academicYear;
                    lessonHours.value = result.data.lessonHours;
                    teacherAssignments.value = result.data.teacherAssignments;

                    // Process days
                    const daysObj = {};
                    result.data.daysOfWeek.forEach((day) => {
                        daysObj[day.value] = day.label;
                    });
                    days.value = daysObj;

                    // Process schedules into a structured format
                    const schedulesObj = {};
                    result.data.lessonHours.forEach((hour) => {
                        schedulesObj[hour.id] = {
                            hour: hour,
                            days: {},
                        };
                    });

                    result.data.schedules.forEach((schedule) => {
                        if (schedulesObj[schedule.lesson_hour_id]) {
                            schedulesObj[schedule.lesson_hour_id].days[
                                schedule.day_of_week
                            ] = schedule;
                        }
                    });

                    schedules.value = schedulesObj;

                    // Count schedules
                    updateScheduleCount();

                    // Generate subject colors
                    generateSubjectColors();
                } else {
                    error.value =
                        result.message || "Failed to load schedule data";
                }
            } catch (err) {
                error.value = "An error occurred while loading data";
                console.error(err);
            } finally {
                loading.value = false;
            }
        };

        // Generate colors for subjects
        const generateSubjectColors = () => {
            const colors = {};
            let colorIndex = 0;

            teacherAssignments.value.forEach((assignment) => {
                if (assignment.subject_id && !colors[assignment.subject_id]) {
                    colors[assignment.subject_id] =
                        colorPalette[colorIndex % colorPalette.length];
                    colorIndex++;
                }
            });

            subjectColors.value = colors;
        };

        // Update schedule count
        const updateScheduleCount = () => {
            let count = 0;
            Object.values(schedules.value).forEach((hourData) => {
                count += Object.values(hourData.days).filter(
                    (schedule) => schedule !== null
                ).length;
            });
            scheduleCount.value = count;
        };

        // Get schedule by hour and day
        const getSchedule = (hourId, dayKey) => {
            return schedules.value[hourId]?.days[dayKey] || null;
        };

        // Get teacher assignment by ID
        const getAssignment = (assignmentId) => {
            return (
                teacherAssignments.value.find((a) => a.id === assignmentId) ||
                null
            );
        };

        // Get schedule style based on subject
        const getScheduleStyle = (hourId, dayKey) => {
            const schedule = getSchedule(hourId, dayKey);
            if (!schedule) return {};

            const assignment = getAssignment(schedule.teacher_assignment_id);
            const color = assignment?.subject_id
                ? subjectColors.value[assignment.subject_id]
                : "#6c757d";

            return {
                borderLeft: `3px solid ${color}`,
                backgroundColor: `${color}20`,
            };
        };

        // Open schedule modal
        const openScheduleModal = (
            mode,
            hourId = null,
            dayKey = null,
            schedule = null
        ) => {
            scheduleModalMode.value = mode;
            currentSchedule.value = schedule;

            scheduleForm.value = {
                teacher_assignment_id: schedule
                    ? schedule.teacher_assignment_id
                    : "",
                lesson_hour_id: schedule
                    ? schedule.lesson_hour_id
                    : hourId || "",
                day_of_week: schedule ? schedule.day_of_week : dayKey || "",
            };

            scheduleErrors.value = {};
            scheduleFormChanged.value = false;

            const modal = new bootstrap.Modal(
                document.getElementById("scheduleModal")
            );
            modal.show();

            // Focus on the first select input
            setTimeout(() => {
                document
                    .querySelector(
                        "#scheduleModal select[name='teacher_assignment_id']"
                    )
                    .focus();
                initializeDataChoices();
            }, 500);
        };

        // Edit schedule
        const editSchedule = (schedule) => {
            openScheduleModal("edit", null, null, schedule);
        };

        // Initialize DataChoices for select inputs
        const initializeDataChoices = () => {
            const selects = document.querySelectorAll(".data-choices");
            selects.forEach((select) => {
                new Choices(select, {
                    searchEnabled: true,
                    placeholder: true,
                    placeholderValue:
                        select.dataset.placeholder || "Select an option",
                    searchPlaceholderValue: "Search...",
                    itemSelectText: "",
                    noResultsText: "No results found",
                    classNames: {
                        containerOuter: "choices",
                        containerInner: "choices__inner",
                        input: "choices__input",
                        list: "choices__list",
                        listDropdown: "choices__list--dropdown",
                        item: "choices__item",
                        itemSelectable: "choices__item--selectable",
                        itemChoice: "choices__item--choice",
                    },
                });
            });
        };

        // Submit schedule form
        const submitScheduleForm = async () => {
            const form = document.querySelector("#scheduleModal form");
            if (!form.checkValidity()) {
                form.classList.add("was-validated");
                return;
            }

            submitting.value = true;
            scheduleErrors.value = {};

            try {
                const formData = new FormData();
                formData.append(
                    "teacher_assignment_id",
                    scheduleForm.value.teacher_assignment_id
                );
                formData.append(
                    "lesson_hour_id",
                    scheduleForm.value.lesson_hour_id
                );
                formData.append("day_of_week", scheduleForm.value.day_of_week);
                formData.append("classroom_id", classroom.value.id);
                formData.append("academic_year_id", academicYear.value.id);

                let url = "/admin/api/class_schedules";
                let method = "POST";

                if (
                    scheduleModalMode.value === "edit" &&
                    currentSchedule.value
                ) {
                    url = `/admin/api/class_schedules/${currentSchedule.value.id}`;
                    formData.append("_method", "PUT");
                }

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        "X-CSRF-TOKEN": csrfToken,
                        Accept: "application/json",
                    },
                    body: formData,
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(
                        data.message || "Schedule saved successfully",
                        "success"
                    );
                    bootstrap.Modal.getInstance(
                        document.getElementById("scheduleModal")
                    ).hide();
                    await refreshData();
                } else {
                    scheduleErrors.value = Object.keys(
                        data.errors || {}
                    ).reduce((acc, key) => {
                        acc[key] = Array.isArray(data.errors[key])
                            ? data.errors[key]
                            : [data.errors[key]];
                        return acc;
                    }, {});
                    throw new Error(data.message || "Failed to save schedule");
                }
            } catch (error) {
                showAlert(error.message, "error");
            } finally {
                submitting.value = false;
            }
        };

        // Confirm delete schedule
        const confirmDeleteSchedule = (schedule) => {
            Swal.fire({
                title: "Confirm Delete",
                text: "This schedule will be permanently deleted. Proceed?",
                icon: "warning",
                showCancelButton: true,
                confirmButtonText: "Yes, Delete!",
                cancelButtonText: "Cancel",
                confirmButtonClass: "btn btn-primary w-xs me-2",
                cancelButtonClass: "btn btn-danger w-xs",
                buttonsStyling: false,
            }).then((result) => {
                if (result.isConfirmed) {
                    deleteSchedule(schedule);
                }
            });
        };

        // Delete schedule
        const deleteSchedule = async (schedule) => {
            try {
                const response = await fetch(
                    `/admin/api/class_schedules/${schedule.id}`,
                    {
                        method: "DELETE",
                        headers: {
                            "X-CSRF-TOKEN": csrfToken,
                            Accept: "application/json",
                        },
                    }
                );

                const data = await response.json();

                if (data.success) {
                    showAlert(
                        data.message || "Schedule deleted successfully",
                        "success"
                    );
                    await refreshData();
                } else {
                    throw new Error(
                        data.message || "Failed to delete schedule"
                    );
                }
            } catch (error) {
                showAlert(error.message, "error");
            }
        };

        // Drag and drop functionality
        const onDragStart = (event, schedule) => {
            draggedSchedule.value = schedule;
            event.dataTransfer.setData("text/plain", schedule.id);
            event.target.classList.add("dragging");
        };

        const onDrop = async (event, hourId, dayKey) => {
            event.preventDefault();
            if (!draggedSchedule.value) return;

            if (
                draggedSchedule.value.lesson_hour_id === hourId &&
                draggedSchedule.value.day_of_week === dayKey
            ) {
                showAlert(
                    "Schedule cannot be moved to the same slot",
                    "warning"
                );
                return;
            }

            if (getSchedule(hourId, dayKey)) {
                showAlert(
                    "This slot is already occupied. Choose an empty slot or edit the schedule.",
                    "warning"
                );
                return;
            }

            loading.value = true;
            try {
                const formData = new FormData();
                formData.append(
                    "teacher_assignment_id",
                    draggedSchedule.value.teacher_assignment_id
                );
                formData.append("lesson_hour_id", hourId);
                formData.append("day_of_week", dayKey);
                formData.append("classroom_id", classroom.value.id);
                formData.append("academic_year_id", academicYear.value.id);
                formData.append("_method", "PUT");

                const response = await fetch(
                    `/admin/api/class_schedules/${draggedSchedule.value.id}`,
                    {
                        method: "POST",
                        headers: {
                            "X-CSRF-TOKEN": csrfToken,
                            Accept: "application/json",
                        },
                        body: formData,
                    }
                );

                const data = await response.json();

                if (data.success) {
                    showAlert("Schedule moved successfully", "success");
                    await refreshData();
                } else {
                    throw new Error(data.message || "Failed to move schedule");
                }
            } catch (error) {
                showAlert(error.message, "error");
            } finally {
                loading.value = false;
                draggedSchedule.value = null;
                document
                    .querySelectorAll(".dragging")
                    .forEach((el) => el.classList.remove("dragging"));
            }
        };

        // Refresh data
        const refreshData = async () => {
            await fetchData();
        };

        // Print page
        const printPage = () => {
            window.print();
        };

        // Show alert
        const showAlert = (message, type = "info") => {
            Swal.fire({
                title:
                    type === "success"
                        ? "Success!"
                        : type === "error"
                        ? "Error!"
                        : "Warning!",
                text: message,
                icon: type,
                confirmButtonClass: "btn btn-primary w-xs",
                buttonsStyling: false,
            });
        };

        // Track form changes
        const updateFormChanged = () => {
            scheduleFormChanged.value = true;
        };

        // Watch form changes
        watch(
            scheduleForm,
            () => {
                updateFormChanged();
            },
            { deep: true }
        );

        // Load data and initialize tooltips on component mount
        onMounted(() => {
            fetchData();
            const modalEl = document.getElementById("scheduleModal");
            modalEl.addEventListener("hide.bs.modal", (event) => {
                if (scheduleFormChanged.value) {
                    event.preventDefault();
                    Swal.fire({
                        title: "Unsaved Changes",
                        text: "You have unsaved changes. Are you sure you want to close?",
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonText: "Yes, Close",
                        cancelButtonText: "Cancel",
                        confirmButtonClass: "btn btn-primary w-xs me-2",
                        cancelButtonClass: "btn btn-danger w-xs",
                        buttonsStyling: false,
                    }).then((result) => {
                        if (result.isConfirmed) {
                            bootstrap.Modal.getInstance(modalEl).hide();
                        }
                    });
                }
            });
            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(
                document.querySelectorAll('[data-bs-toggle="tooltip"]')
            );
            tooltipTriggerList.map(
                (tooltipTriggerEl) => new bootstrap.Tooltip(tooltipTriggerEl)
            );
        });

        // Return all reactive properties and methods
        return {
            loading,
            error,
            classroom,
            academicYear,
            lessonHours,
            teacherAssignments,
            schedules,
            days,
            activeTab,
            scheduleCount,
            submitting,
            scheduleForm,
            scheduleErrors,
            scheduleModalMode,
            currentSchedule,
            getSchedule,
            getAssignment,
            getScheduleStyle,
            openScheduleModal,
            editSchedule,
            submitScheduleForm,
            confirmDeleteSchedule,
            refreshData,
            printPage,
            onDragStart,
            onDrop,
            formatTime,
        };
    },
    template: `
        <div>
            <style>
                .table-responsive {
                    overflow-x: auto;
                    border-radius: 8px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                }
                .schedule-cell {
                    transition: background-color 0.2s ease;
                }
                .schedule-cell:hover {
                    background-color: #e9ecef;
                    border: 2px dashed #007bff;
                }
                .schedule-item:hover .schedule-actions,
                .schedule-empty-cell:hover .add-schedule-btn {
                    display: block !important;
                }
                .btn {
                    border-radius: 6px;
                    transition: all 0.2s ease;
                }
                .btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }
                @media (max-width: 768px) {
                    .table th, .table td {
                        font-size: 12px;
                        padding: 8px;
                    }
                    .schedule-item {
                        font-size: 11px;
                    }
                }
                @media (max-width: 576px) {
                    .modal-dialog {
                        margin: 0.5rem;
                    }
                    .modal-body {
                        padding: 1rem;
                    }
                }
                @media print {
                    .btn, .schedule-actions, .add-schedule-btn {
                        display: none !important;
                    }
                    .table {
                        font-size: 10pt;
                    }
                    .table th, .table td {
                        border: 1px solid #000 !important;
                    }
                    .schedule-item {
                        background-color: transparent !important;
                        border-left: none !important;
                    }
                }
            </style>

            <div v-if="loading" class="text-center py-5">
                <div class="spinner-grow text-primary" role="status"></div>
                <div class="spinner-grow text-primary mx-2" role="status"></div>
                <div class="spinner-grow text-primary" role="status"></div>
                <p class="mt-3 text-muted">Loading schedule data...</p>
            </div>
            <div v-else-if="!lessonHours.length" class="text-center py-5">
                <div class="avatar-xl mx-auto mb-3">
                    <div class="avatar-title bg-light text-muted rounded-circle">
                        <i class="ri-calendar-schedule-line fs-2"></i>
                    </div>
                </div>
                <h5>No Lesson Hours Available</h5>
                <p class="text-muted mb-4">Add lesson hours to start creating schedules for your class.</p>
                <a href="/admin/lesson-hours/create" class="btn btn-primary">
                    <i class="ri-add-line me-1"></i> Add Lesson Hours
                </a>
            </div>
            <div v-else>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Weekly Lesson Schedule</h6>
                    <button class="btn btn-success" @click="openScheduleModal('create')" :disabled="loading || !lessonHours.length">
                        <i class="ri-calendar-schedule-line me-1"></i> Add Schedule
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered table-sm align-middle mb-0">
                        <thead class="table-light text-center">
                            <tr>
                                <th width="12%">Time</th>
                                <th v-for="(dayName, dayKey) in days" :key="dayKey">{{ dayName }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="hour in lessonHours" :key="hour.id">
                                <td class="text-center fw-medium bg-light">
                                    <div class="small fw-semibold">{{ hour.name }}</div>
                                    <div class="text-muted" style="font-size: 11px;">
                                        {{ formatTime(hour.start_time) }} - {{ formatTime(hour.end_time) }}
                                    </div>
                                </td>
                                <td
                                    v-for="(dayName, dayKey) in days"
                                    :key="hour.id + '-' + dayKey"
                                    class="position-relative schedule-cell"
                                    :class="getSchedule(hour.id, dayKey) ? 'p-2 has-schedule' : 'text-center schedule-empty-cell'"
                                    style="min-height: 70px; vertical-align: middle;"
                                    :data-hour-id="hour.id"
                                    :data-day-key="dayKey"
                                    @drop="onDrop($event, hour.id, dayKey)"
                                    @dragover.prevent
                                    @dragenter.prevent
                                >
                                    <div
                                        v-if="getSchedule(hour.id, dayKey)"
                                        class="schedule-item p-2 rounded position-relative h-100"
                                        :style="getScheduleStyle(hour.id, dayKey)"
                                        :draggable="true"
                                        :data-schedule-id="getSchedule(hour.id, dayKey).id"
                                        @dragstart="onDragStart($event, getSchedule(hour.id, dayKey))"
                                        @click="editSchedule(getSchedule(hour.id, dayKey))"
                                    >
                                        <div class="fw-semibold small">
                                            {{ getAssignment(getSchedule(hour.id, dayKey).teacher_assignment_id)?.subject_name || 'N/A' }}
                                        </div>
                                        <div class="text-muted" style="font-size: 11px;">
                                            {{ getAssignment(getSchedule(hour.id, dayKey).teacher_assignment_id)?.teacher_name || 'N/A' }}
                                        </div>
                                        <div class="schedule-actions position-absolute" style="top: 5px; right: 5px;">
                                            <div class="btn-group">
                                                <button
                                                    class="btn btn-sm btn-outline-primary"
                                                    @click.stop="editSchedule(getSchedule(hour.id, dayKey))"
                                                    data-bs-toggle="tooltip"
                                                    title="Edit Schedule"
                                                >
                                                    <i class="ri-pencil-line"></i>
                                                </button>
                                                <button
                                                    class="btn btn-sm btn-outline-danger"
                                                    @click.stop="confirmDeleteSchedule(getSchedule(hour.id, dayKey))"
                                                    data-bs-toggle="tooltip"
                                                    title="Delete Schedule"
                                                >
                                                    <i class="ri-delete-bin-line"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        v-else
                                        class="btn btn-sm btn-outline-primary add-schedule-btn"
                                        @click="openScheduleModal('create', hour.id, dayKey)"
                                    >
                                        <i class="ri-add-circle-line"></i> Add
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Schedule Modal -->
            <div class="modal fade" id="scheduleModal" tabindex="-1" aria-labelledby="scheduleModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="scheduleModalLabel">
                                <i class="ri-calendar-schedule-line me-2"></i>
                                {{ scheduleModalMode === 'create' ? 'Add Schedule' : 'Edit Schedule' }}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close Schedule Modal"></button>
                        </div>
                        <form @submit.prevent="submitScheduleForm" class="needs-validation" novalidate>
                            <div class="modal-body">
                                <div class="alert alert-info border-0" role="alert">
                                    <div class="d-flex">
                                        <i class="ri-information-line fs-16 me-2"></i>
                                        <div>
                                            <h6 class="alert-heading mb-1">Schedule Information</h6>
                                            <p class="mb-0">
                                                {{ scheduleModalMode === 'create' ? 'Adding' : 'Editing' }} schedule for class {{ classroom?.name }} in academic year {{ academicYear?.name }}.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Teacher & Subject <span class="text-danger">*</span></label>
                                        <select
                                            name="teacher_assignment_id"
                                            class="form-select data-choices"
                                            v-model="scheduleForm.teacher_assignment_id"
                                            :class="{ 'is-invalid': scheduleErrors.teacher_assignment_id }"
                                            required
                                            aria-label="Select Teacher and Subject"
                                            data-placeholder="Select Teacher & Subject"
                                        >
                                            <option value="">Select Teacher & Subject</option>
                                            <option v-for="assignment in teacherAssignments" :key="assignment.id" :value="assignment.id">
                                                {{ assignment.teacher_name }} - {{ assignment.subject_name || 'N/A' }}
                                            </option>
                                        </select>
                                        <div v-if="scheduleErrors.teacher_assignment_id" class="invalid-feedback">
                                            {{ scheduleErrors.teacher_assignment_id.join(', ') }}
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Lesson Hour <span class="text-danger">*</span></label>
                                        <select
                                            name="lesson_hour_id"
                                            class="form-select data-choices"
                                            v-model="scheduleForm.lesson_hour_id"
                                            :class="{ 'is-invalid': scheduleErrors.lesson_hour_id }"
                                            required
                                            aria-label="Select Lesson Hour"
                                            data-placeholder="Select Lesson Hour"
                                        >
                                            <option value="">Select Lesson Hour</option>
                                            <option v-for="hour in lessonHours" :key="hour.id" :value="hour.id">
                                                {{ hour.name }} ({{ formatTime(hour.start_time) }} - {{ formatTime(hour.end_time) }})
                                            </option>
                                        </select>
                                        <div v-if="scheduleErrors.lesson_hour_id" class="invalid-feedback">
                                            {{ scheduleErrors.lesson_hour_id.join(', ') }}
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Day <span class="text-danger">*</span></label>
                                        <select
                                            name="day_of_week"
                                            class="form-select data-choices"
                                            v-model="scheduleForm.day_of_week"
                                            :class="{ 'is-invalid': scheduleErrors.day_of_week }"
                                            required
                                            aria-label="Select Day"
                                            data-placeholder="Select Day"
                                        >
                                            <option value="">Select Day</option>
                                            <option v-for="(dayName, dayKey) in days" :key="dayKey" :value="dayKey">
                                                {{ dayName }}
                                            </option>
                                        </select>
                                        <div v-if="scheduleErrors.day_of_week" class="invalid-feedback">
                                            {{ scheduleErrors.day_of_week.join(', ') }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-ghost-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-success" :disabled="submitting">
                                    <i class="ri-save-line me-1"></i>
                                    <span v-if="submitting">Saving...</span>
                                    <span v-else>Save</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `,
};

// Mount the Vue app
const app = createApp(ScheduleApp);
app.mount("#schedule-app");
