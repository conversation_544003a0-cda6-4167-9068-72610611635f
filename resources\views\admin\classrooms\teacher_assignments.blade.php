@extends('admin.layouts.app')

@section('title', 'Pengajar Kelas ' . $classroom->name)

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Pengajar Kelas ' . $classroom->name,
        'breadcrumb' => 'Manajemen Akademik',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">Pengajar Kelas {{ $classroom->name }} - {{ $classroom->academicYear->name }} {{ $classroom->academicYear->semester->label() }}</h5>
                        <div class="flex-shrink-0">
                            <div class="d-flex flex-wrap gap-2">
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTeacherAssignmentModal">
                                    <i class="ri-add-line align-bottom me-1"></i> Tambah Pengajar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive table-card">
                        <table id="teacher-assignments-table" class="table align-middle table-nowrap" style="width: 100%;">
                            <thead class="table-light text-muted">
                                <tr>
                                    <th scope="col" style="width: 65px;">No</th>
                                    <th>Guru</th>
                                    <th>Mata Pelajaran</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Teacher Assignment Modal -->
    <div class="modal fade" id="addTeacherAssignmentModal" tabindex="-1" aria-labelledby="addTeacherAssignmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addTeacherAssignmentModalLabel">Tambah Pengajar</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="add-teacher-assignment-form" method="POST" action="{{ route('admin.classrooms.teacher-assignments.store', $classroom->id) }}">
                    <div class="modal-body">
                        @csrf
                        <div class="mb-3">
                            <label for="teacher_id" class="form-label">Guru <span class="text-danger">*</span></label>
                            <select class="form-select" data-choices id="teacher_id" name="teacher_id" required>
                                <option value="" selected disabled>Pilih Guru</option>
                                @foreach ($teachers as $teacher)
                                    <option value="{{ $teacher->id }}">{{ $teacher->user->name }}</option>
                                @endforeach
                            </select>
                            <div class="invalid-feedback" id="teacher_id-error"></div>
                        </div>
                        <div class="mb-3">
                            <label for="subject_id" class="form-label">Mata Pelajaran <span class="text-danger">*</span></label>
                            <select class="form-select" data-choices id="subject_id" name="subject_id" required>
                                <option value="" selected disabled>Pilih Mata Pelajaran</option>
                                @foreach ($subjects as $subject)
                                    <option value="{{ $subject->id }}">{{ $subject->name }}</option>
                                @endforeach
                            </select>
                            <div class="invalid-feedback" id="subject_id-error"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-ghost-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary" id="add-btn">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Teacher Assignment Modal -->
    <div class="modal fade" id="editTeacherAssignmentModal" tabindex="-1" aria-labelledby="editTeacherAssignmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editTeacherAssignmentModalLabel">Edit Pengajar</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="edit-teacher-assignment-form" method="POST">
                    <div class="modal-body">
                        @csrf
                        @method('PUT')
                        <input type="hidden" id="edit_assignment_id" name="assignment_id">
                        <div class="mb-3">
                            <label for="edit_teacher_id" class="form-label">Guru <span class="text-danger">*</span></label>
                            <select class="form-select" data-choices id="edit_teacher_id" name="teacher_id" required>
                                <option value="" selected disabled>Pilih Guru</option>
                                @foreach ($teachers as $teacher)
                                    <option value="{{ $teacher->id }}">{{ $teacher->user->name }}</option>
                                @endforeach
                            </select>
                            <div class="invalid-feedback" id="edit_teacher_id-error"></div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_subject_id" class="form-label">Mata Pelajaran <span class="text-danger">*</span></label>
                            <select class="form-select" data-choices id="edit_subject_id" name="subject_id" required>
                                <option value="" selected disabled>Pilih Mata Pelajaran</option>
                                @foreach ($subjects as $subject)
                                    <option value="{{ $subject->id }}">{{ $subject->name }}</option>
                                @endforeach
                            </select>
                            <div class="invalid-feedback" id="edit_subject_id-error"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-ghost-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary" id="update-btn">Perbarui</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@include('admin.partials.plugins._jquery')
@include('admin.partials.plugins._datatables')

@push('scripts')
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            const table = $('#teacher-assignments-table').DataTable({
                lengthChange: false,
                searching: false,
                ordering: false,
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('admin.classrooms.teacher-assignments', $classroom->id) }}",
                    error: xhr => {
                        const message = xhr.responseJSON?.message || 'Gagal memuat data pengajar.';
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'teacher_name',
                        name: 'teacher_name'
                    },
                    {
                        data: 'subject_name',
                        name: 'subject_name'
                    },
                    {
                        data: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: "Cari...",
                    lengthMenu: "Tampilkan _MENU_ data",
                    zeroRecords: "Data tidak ditemukan",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "(disaring dari _MAX_ total data)",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya",
                    }
                },
            });

            // Add Teacher Assignment
            $('#add-teacher-assignment-form').on('submit', function(e) {
                e.preventDefault();

                const form = $(this);
                const url = form.attr('action');
                const formData = form.serialize();

                $.ajax({
                    type: 'POST',
                    url: url,
                    data: formData,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        $('#addTeacherAssignmentModal').modal('hide');
                        form[0].reset();
                        table.ajax.reload();

                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message || 'Pengajar berhasil ditambahkan',
                            icon: 'success',
                            showConfirmButton: false,
                            timer: 1500
                        });
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;
                            $.each(errors, function(key, value) {
                                $('#' + key).addClass('is-invalid');
                                $('#' + key + '-error').text(value[0]);
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: xhr.responseJSON?.message || 'Terjadi kesalahan saat menambahkan pengajar',
                                icon: 'error'
                            });
                        }
                    }
                });
            });

            // Edit Teacher Assignment - Populate Modal
            $(document).on('click', '.btn-edit-assignment', function() {
                const id = $(this).data('id');
                const teacherId = $(this).data('teacher-id');
                const subjectId = $(this).data('subject-id');

                $('#edit_assignment_id').val(id);
                $('#edit_teacher_id').val(teacherId);
                $('#edit_subject_id').val(subjectId);

                $('#edit-teacher-assignment-form').attr('action',
                    "{{ route('admin.classrooms.teacher-assignments.update', ['classroom' => $classroom->id, 'assignment' => ':id']) }}".replace(':id', id));
            });

            // Update Teacher Assignment
            $('#edit-teacher-assignment-form').on('submit', function(e) {
                e.preventDefault();

                const form = $(this);
                const url = form.attr('action');
                const formData = form.serialize();

                $.ajax({
                    type: 'PUT',
                    url: url,
                    data: formData,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        $('#editTeacherAssignmentModal').modal('hide');
                        table.ajax.reload();

                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message || 'Pengajar berhasil diperbarui',
                            icon: 'success',
                            showConfirmButton: false,
                            timer: 1500
                        });
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;
                            $.each(errors, function(key, value) {
                                $('#edit_' + key).addClass('is-invalid');
                                $('#edit_' + key + '-error').text(value[0]);
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: xhr.responseJSON?.message || 'Terjadi kesalahan saat memperbarui pengajar',
                                icon: 'error'
                            });
                        }
                    }
                });
            });

            // Delete Teacher Assignment
            $(document).on('click', '.btn-delete-assignment', function() {
                const url = $(this).data('url');

                Swal.fire({
                    title: 'Apakah Anda yakin?',
                    text: "Pengajar akan dihapus dari kelas ini!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Ya, hapus!',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            type: 'DELETE',
                            url: url,
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                table.ajax.reload();

                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: response.message || 'Pengajar berhasil dihapus',
                                    icon: 'success',
                                    showConfirmButton: false,
                                    timer: 1500
                                });
                            },
                            error: function(xhr) {
                                Swal.fire({
                                    title: 'Gagal!',
                                    text: xhr.responseJSON?.message || 'Terjadi kesalahan saat menghapus pengajar',
                                    icon: 'error'
                                });
                            }
                        });
                    }
                });
            });

            // Reset form when modal is closed
            $('#addTeacherAssignmentModal').on('hidden.bs.modal', function() {
                $('#add-teacher-assignment-form')[0].reset();
                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').text('');
            });

            $('#editTeacherAssignmentModal').on('hidden.bs.modal', function() {
                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').text('');
            });
        });
    </script>
@endpush
