<?php

namespace App\Http\Controllers\Admin;

use App\Enums\DayOfWeekEnum;
use App\Enums\ScheduleStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Requests\ClassScheduleRequests\ClassScheduleStoreRequest;
use App\Http\Requests\Requests\ClassScheduleRequests\ClassScheduleUpdateRequest;
use App\Models\AcademicYear;
use App\Models\Classroom;
use App\Models\ClassSchedule;
use App\Models\LessonHour;
use App\Models\TeacherAssignment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class ClassScheduleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->datatables($request);
        }

        return view('admin.class_schedules.index', [
            'classrooms' => Classroom::all(),
            'academicYears' => AcademicYear::all(),
            'daysOfWeek' => DayOfWeekEnum::options(),
            'scheduleStatuses' => ScheduleStatusEnum::options(),
        ]);
    }

    /**
     * Process datatables ajax request.
     */
    public function datatables($req)
    {
        $query = ClassSchedule::with(['teacherAssignment.teacher.user', 'teacherAssignment.subject', 'lessonHour']);

        // Filter by classroom and academic year if provided
        if ($req->has('classroom_id') && !empty($req->classroom_id)) {
            $query->whereHas('teacherAssignment', function ($q) use ($req) {
                $q->where('classroom_id', $req->classroom_id);
            });
        }

        if ($req->has('academic_year_id') && !empty($req->academic_year_id)) {
            $query->whereHas('teacherAssignment', function ($q) use ($req) {
                $q->where('academic_year_id', $req->academic_year_id);
            });
        }

        // Filter by day of week if provided
        if ($req->has('day_of_week') && !empty($req->day_of_week)) {
            $query->where('day_of_week', $req->day_of_week);
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('teacher_name', function ($row) {
                return $row->teacherAssignment->teacher->user->name ?? '-';
            })
            ->addColumn('subject_name', function ($row) {
                return $row->teacherAssignment->subject->name ?? '-';
            })
            ->addColumn('day', function ($row) {
                return $row->day_of_week->label();
            })
            ->addColumn('time', function ($row) {
                return $row->lessonHour->start_time->format('H:i') . ' - ' . $row->lessonHour->end_time->format('H:i');
            })
            ->addColumn('status', function ($row) {
                $color = $row->schedule_status->color();
                return '<span class="badge bg-' . $color . '-subtle text-' . $color . '">' . $row->schedule_status->label() . '</span>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.class_schedules._action', [
                    'edit' => route('admin.class_schedules.edit', $row->id),
                    'destroy' => route('admin.class_schedules.destroy', $row->id),
                    'id' => $row->id,
                ]);
            })
            ->rawColumns(['status', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Get all teacher assignments with relations
        $teacherAssignments = TeacherAssignment::with(['teacher.user', 'subject', 'classroom', 'academicYear'])->get();

        // Get global lesson hours (those not tied to any classroom or shift)
        $lessonHours = LessonHour::whereNull('classroom_id')
            ->whereNull('shift_id')
            ->orderBy('sequence')
            ->get();

        return view('admin.class_schedules.create', [
            'teacherAssignments' => $teacherAssignments,
            'lessonHours' => $lessonHours,
            'daysOfWeek' => DayOfWeekEnum::options(),
            'scheduleStatuses' => ScheduleStatusEnum::options(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ClassScheduleStoreRequest $request)
    {
        // Get validated data
        $data = $request->validated();

        // Check if schedule with same time slot already exists
        $scheduleExists = ClassSchedule::where([
            'lesson_hour_id' => $data['lesson_hour_id'],
            'day_of_week' => $data['day_of_week'],
        ])->exists();

        if ($scheduleExists) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Jadwal dengan slot waktu yang sama sudah ada.');
        }

        // Create new schedule using transaction
        DB::transaction(function () use ($data) {
            ClassSchedule::create($data);
        });

        return redirect()->route('admin.class_schedules.index')
            ->with('success', 'Jadwal pelajaran berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $classSchedule = ClassSchedule::findOrFail($id);
        $teacherAssignment = TeacherAssignment::findOrFail($classSchedule->teacher_assignment_id);
        $classroom = Classroom::findOrFail($teacherAssignment->classroom_id);

        // Get lesson hours for the classroom or shift, or global if none exist
        $lessonHours = LessonHour::where(function ($query) use ($classroom) {
            $query->where('classroom_id', $classroom->id)
                ->orWhere(function ($q) use ($classroom) {
                    if ($classroom->shift_id) {
                        $q->whereNull('classroom_id')
                            ->where('shift_id', $classroom->shift_id);
                    }
                });
        })
            ->orderBy('sequence')
            ->get();

        // If no specific lesson hours found, use global lesson hours
        if ($lessonHours->isEmpty()) {
            $lessonHours = LessonHour::whereNull('classroom_id')
                ->whereNull('shift_id')
                ->orderBy('sequence')
                ->get();
        }

        return view('admin.class_schedules.edit', [
            'classSchedule' => $classSchedule,
            'teacherAssignments' => TeacherAssignment::with(['teacher.user', 'subject', 'classroom', 'academicYear'])->get(),
            'lessonHours' => $lessonHours,
            'daysOfWeek' => DayOfWeekEnum::options(),
            'scheduleStatuses' => ScheduleStatusEnum::options(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ClassScheduleUpdateRequest $request, string $id)
    {
        // Get validated data
        $data = $request->validated();

        // Find the schedule
        $classSchedule = ClassSchedule::findOrFail($id);

        // Check if schedule with same time slot already exists (excluding current schedule)
        $scheduleExists = ClassSchedule::where([
            'lesson_hour_id' => $data['lesson_hour_id'],
            'day_of_week' => $data['day_of_week'],
        ])
            ->where('id', '!=', $id)
            ->exists();

        if ($scheduleExists) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Jadwal dengan slot waktu yang sama sudah ada.');
        }

        // Update schedule using transaction
        DB::transaction(function () use ($classSchedule, $data) {
            $classSchedule->update($data);
        });

        return redirect()->route('admin.class_schedules.index')
            ->with('success', 'Jadwal pelajaran berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $classSchedule = ClassSchedule::findOrFail($id);
            $classSchedule->delete();

            return response()->json([
                'success' => true,
                'message' => 'Jadwal pelajaran berhasil dihapus.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus jadwal pelajaran: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display schedule by classroom and academic year.
     */
    public function viewSchedule(Request $request)
    {
        $request->validate([
            'classroom_id' => 'required|exists:classrooms,id',
            'academic_year_id' => 'required|exists:academic_years,id',
        ]);

        $classroom = Classroom::findOrFail($request->classroom_id);
        $academicYear = AcademicYear::findOrFail($request->academic_year_id);

        // Get lesson hours for the classroom or shift, or global if none exist
        $lessonHours = LessonHour::where(function ($query) use ($classroom) {
            $query->where('classroom_id', $classroom->id)
                ->orWhere(function ($q) use ($classroom) {
                    if ($classroom->shift_id) {
                        $q->whereNull('classroom_id')
                            ->where('shift_id', $classroom->shift_id);
                    }
                });
        })
            ->orderBy('sequence')
            ->get();

        // If no specific lesson hours found, use global lesson hours
        if ($lessonHours->isEmpty()) {
            $lessonHours = LessonHour::whereNull('classroom_id')
                ->whereNull('shift_id')
                ->orderBy('sequence')
                ->get();
        }

        // Get teacher assignments for the classroom and academic year
        $teacherAssignments = TeacherAssignment::with(['teacher.user', 'subject'])
            ->where('classroom_id', $classroom->id)
            ->where('academic_year_id', $academicYear->id)
            ->get();

        // Get schedules for the classroom and academic year
        $schedules = ClassSchedule::with(['teacherAssignment.teacher.user', 'teacherAssignment.subject', 'lessonHour'])
            ->whereHas('teacherAssignment', function ($query) use ($classroom, $academicYear) {
                $query->where('classroom_id', $classroom->id)
                    ->where('academic_year_id', $academicYear->id);
            })
            ->get();

        return view('admin.class_schedules.view', [
            'classroom' => $classroom,
            'academicYear' => $academicYear,
            'lessonHours' => $lessonHours,
            'teacherAssignments' => $teacherAssignments,
            'schedules' => $schedules,
            'daysOfWeek' => DayOfWeekEnum::cases(),
        ]);
    }
}
