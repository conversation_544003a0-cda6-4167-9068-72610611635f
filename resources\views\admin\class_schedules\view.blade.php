@extends('admin.layouts.app')

@section('title', '<PERSON><PERSON><PERSON> - ' . $classroom->name)

@section('content')
    @include('admin.components.page-title', ['title' => '<PERSON><PERSON><PERSON> Pelajar<PERSON> - ' . $classroom->name, 'breadcrumb' => '<PERSON><PERSON><PERSON> Pelajaran'])

    <!-- Classroom Info Card -->
    <div class="row mb-4">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar-lg">
                                <div class="avatar-title bg-primary-subtle text-primary rounded-circle">
                                    <i class="ri-building-4-line fs-24"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="mb-1">{{ $classroom->name }}</h5>
                            <p class="text-muted mb-2">{{ $academicYear->name }} - {{ $academicYear->semester->label() }}</p>
                            <div class="hstack gap-3 text-muted">
                                <span><i class="ri-user-star-line me-1"></i>{{ $classroom->teacher->user->name ?? 'Belum ditugaskan' }}</span>
                                <span><i class="ri-group-line me-1"></i>{{ $classroom->students_count ?? 0 }} Siswa</span>
                                <span><i class="ri-book-line me-1"></i>{{ $classroom->program->name ?? 'N/A' }}</span>
                                <span><i class="ri-time-line me-1"></i>{{ $classroom->shift?->name ?? 'N/A' }}</span>
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="hstack gap-2">
                                <a href="{{ route('admin.class_schedules.index') }}" class="btn btn-soft-secondary">
                                    <i class="ri-arrow-left-line align-bottom me-1"></i> Kembali
                                </a>
                                <button class="btn btn-ghost-info" onclick="window.print()">
                                    <i class="ri-printer-line align-bottom"></i> Cetak
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <div>
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                Jadwal Pelajaran Mingguan
                            </h5>
                            <p class="text-muted mb-0 mt-1">Kelola jadwal pelajaran untuk kelas ini</p>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="schedule-app" data-classroom-id="{{ $classroom->id }}" data-academic-year-id="{{ $academicYear->id }}">
                        <!-- Vue.js app will be mounted here -->
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Memuat jadwal pelajaran...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        .schedule-grid {
            display: grid;
            grid-template-columns: 100px repeat(7, 1fr);
            gap: 5px;
        }

        .schedule-header {
            background-color: #f8f9fa;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            border-radius: 4px;
        }

        .schedule-time {
            background-color: #f8f9fa;
            padding: 10px;
            text-align: center;
            border-radius: 4px;
        }

        .schedule-cell {
            min-height: 60px;
            border: 1px dashed #dee2e6;
            border-radius: 4px;
            padding: 5px;
            transition: all 0.3s;
            position: relative;
            vertical-align: middle;
        }

        .schedule-cell.droppable-hover {
            background-color: rgba(13, 110, 253, 0.1);
            border: 1px dashed #0d6efd;
        }

        .schedule-item {
            background-color: #fff;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 5px;
            cursor: move;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border-left: 3px solid #0d6efd;
            min-height: 50px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            transition: all 0.15s ease;
            position: relative;
        }

        .schedule-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .schedule-item.active {
            border-left-color: #198754;
        }

        .schedule-item.cancelled {
            border-left-color: #dc3545;
        }

        .schedule-item.pending {
            border-left-color: #ffc107;
        }

        .schedule-item.completed {
            border-left-color: #0dcaf0;
        }

        .schedule-item-subject {
            font-weight: bold;
            font-size: 0.9rem;
            margin-bottom: 3px;
        }

        .schedule-item-teacher {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .teacher-assignment-item {
            background-color: #fff;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 5px;
            cursor: move;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border-left: 3px solid #6f42c1;
        }

        .schedule-actions {
            position: absolute;
            top: 2px;
            right: 2px;
            display: none;
        }

        .schedule-item:hover .schedule-actions {
            display: block !important;
        }

        .schedule-actions .btn {
            padding: 0.125rem 0.25rem;
            font-size: 0.6875rem;
        }

        .schedule-empty-cell:hover {
            background-color: rgba(var(--vz-primary-rgb), 0.05);
        }

        .schedule-empty-cell:hover .add-schedule-btn {
            display: inline-block !important;
        }

        .add-schedule-btn {
            display: none;
        }

        .schedule-item.dragging {
            opacity: 0.5;
            border: 2px dashed var(--vz-primary);
        }

        .schedule-empty-cell.drop-target {
            background-color: rgba(var(--vz-success-rgb), 0.1);
            border: 2px dashed var(--vz-success);
        }

        @media print {

            .btn,
            .schedule-actions,
            .add-schedule-btn,
            .card-header {
                display: none !important;
            }

            .page-content {
                margin-left: 0 !important;
                padding: 0 !important;
            }

            .card {
                box-shadow: none !important;
                border: none !important;
            }
        }
    </style>
@endpush

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/vue@3.2.47/dist/vue.global.prod.js"></script>
    <script src="{{ asset('js/class-schedule.js') }}"></script>
@endpush
