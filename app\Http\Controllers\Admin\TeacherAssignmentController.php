<?php

namespace App\Http\Controllers\Admin;

use App\Models\Teacher;
use App\Models\Subject;
use App\Models\Classroom;
use App\Models\AcademicYear;
use App\Models\TeacherAssignment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Requests\Requests\TeacherAssignmentRequests\TeacherAssignmentStoreRequest;
use App\Http\Requests\Requests\TeacherAssignmentRequests\TeacherAssignmentUpdateRequest;

class TeacherAssignmentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->datatables($request);
        }

        return view('admin.teacher_assignments.index', [
            'teachers' => Teacher::with('user')->get(),
            'subjects' => Subject::all(),
            'classrooms' => Classroom::all(),
            'academicYears' => AcademicYear::all(),
        ]);
    }

    public function datatables($req)
    {
        $query = TeacherAssignment::with(['teacher', 'subject', 'classroom', 'academicYear']);

        return DataTables::eloquent($query)
            ->addIndexColumn()
            ->addColumn('teacher_name', function ($row) {
                return $row->teacher->user->name;
            })
            ->addColumn('subject_name', function ($row) {
                return $row->subject->name;
            })
            ->addColumn('classroom_name', function ($row) {
                return $row->classroom->name;
            })
            ->addColumn('academic_year_name', function ($row) {
                return $row->academicYear->name . ' - ' . $row->academicYear->semester->label();
            })
            ->addColumn('action', function ($row) {
                return view('admin.teacher_assignments._action', [
                    'edit' => route('admin.teacher_assignments.edit', $row->id),
                    'destroy' => route('admin.teacher_assignments.destroy', $row->id),
                    'id' => $row->id,
                ]);
            })
            // Filter by teacher
            ->filterColumn('teacher_name', function ($q, $keyword) {
                if (!empty($keyword) && $keyword !== 'all') {
                    $q->whereHas('teacher', function ($query) use ($keyword) {
                        $query->where('id', $keyword);
                    });
                }
            })
            // Filter by subject
            ->filterColumn('subject_name', function ($q, $keyword) {
                if (!empty($keyword) && $keyword !== 'all') {
                    $q->whereHas('subject', function ($query) use ($keyword) {
                        $query->where('id', $keyword);
                    });
                }
            })
            // Filter by classroom
            ->filterColumn('classroom_name', function ($q, $keyword) {
                if (!empty($keyword) && $keyword !== 'all') {
                    $q->whereHas('classroom', function ($query) use ($keyword) {
                        $query->where('id', $keyword);
                    });
                }
            })
            // Filter by academic year
            ->filterColumn('academic_year_name', function ($q, $keyword) {
                if (!empty($keyword) && $keyword !== 'all') {
                    $q->whereHas('academicYear', function ($query) use ($keyword) {
                        $query->where('id', $keyword);
                    });
                }
            })
            // Search global untuk semua kolom
            ->filter(function ($q) use ($req) {
                if ($search = $req->input('search.value')) {
                    $q->where(function ($sub) use ($search) {
                        $sub->whereHas('teacher', function ($query) use ($search) {
                            $query->whereHas('user', function ($query) use ($search) {
                                $query->where('name', 'like', "%{$search}%");
                            });
                        })
                            ->orWhereHas('subject', function ($query) use ($search) {
                                $query->where('name', 'like', "%{$search}%");
                            })
                            ->orWhereHas('classroom', function ($query) use ($search) {
                                $query->where('name', 'like', "%{$search}%");
                            })
                            ->orWhereHas('academicYear', function ($query) use ($search) {
                                $query->where('name', 'like', "%{$search}%");
                            });
                    });
                }
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.teacher_assignments.create', [
            'teachers' => Teacher::with('user')->get(),
            'subjects' => Subject::all(),
            'classrooms' => Classroom::all(),
            'academicYears' => AcademicYear::all(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(TeacherAssignmentStoreRequest $request)
    {
        // Mengambil data yang sudah divalidasi dari request.
        $data = $request->validated();

        // Memeriksa apakah penugasan guru dengan kombinasi yang sama sudah ada.
        $assignmentExists = TeacherAssignment::where([
            'teacher_id' => $data['teacher_id'],
            'subject_id' => $data['subject_id'],
            'classroom_id' => $data['classroom_id'],
            'academic_year_id' => $data['academic_year_id'],
        ])->exists();

        if ($assignmentExists) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Penugasan guru dengan kombinasi yang sama sudah ada.');
        }

        // Menggunakan transaction untuk memastikan operasi berhasil
        DB::transaction(function () use ($data) {
            // Buat penugasan guru baru.
            TeacherAssignment::create($data);
        });

        return redirect()->route('admin.teacher_assignments.index')
            ->with('success', 'Penugasan guru berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        return view('admin.teacher_assignments.edit', [
            'teachers' => Teacher::with('user')->get(),
            'subjects' => Subject::all(),
            'classrooms' => Classroom::all(),
            'academicYears' => AcademicYear::all(),
            'teacherAssignment' => TeacherAssignment::findOrFail($id),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(TeacherAssignmentUpdateRequest $request, string $id)
    {
        // Mengambil data yang sudah divalidasi dari request.
        $data = $request->validated();

        // Mencari data penugasan guru berdasarkan ID.
        $teacherAssignment = TeacherAssignment::findOrFail($id);

        // Memeriksa apakah penugasan guru dengan kombinasi yang sama sudah ada,
        // tetapi mengecualikan penugasan guru yang sedang di-update.
        $assignmentExists = TeacherAssignment::where([
            'teacher_id' => $data['teacher_id'],
            'subject_id' => $data['subject_id'],
            'classroom_id' => $data['classroom_id'],
            'academic_year_id' => $data['academic_year_id'],
        ])
            ->where('id', '!=', $id)
            ->exists();

        // Jika data duplikat ditemukan, kembalikan dengan pesan error.
        if ($assignmentExists) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Penugasan guru dengan kombinasi yang sama sudah ada.');
        }

        // Menggunakan transaction untuk memastikan pembaruan data berjalan dengan aman.
        DB::transaction(function () use ($teacherAssignment, $data) {
            // Lakukan pembaruan data.
            $teacherAssignment->update($data);
        });

        return redirect()->route('admin.teacher_assignments.index')
            ->with('success', 'Penugasan guru berhasil diubah.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Hapus data penugasan guru.
        TeacherAssignment::findOrFail($id)->delete();

        return response()->json([
            'message' => 'Penugasan guru berhasil dihapus.'
        ], 200);
    }
}
