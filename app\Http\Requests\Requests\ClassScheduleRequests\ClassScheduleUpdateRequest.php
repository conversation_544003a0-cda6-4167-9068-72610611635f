<?php

namespace App\Http\Requests\Requests\ClassScheduleRequests;

use App\Enums\DayOfWeekEnum;
use App\Enums\ScheduleStatusEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class ClassScheduleUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'teacher_assignment_id' => ['required', 'exists:teacher_assignments,id'],
            'lesson_hour_id' => ['required', 'exists:lesson_hours,id'],
            'day_of_week' => ['required', new Enum(DayOfWeekEnum::class)],
            'schedule_status' => ['required', new Enum(ScheduleStatusEnum::class)],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'teacher_assignment_id' => 'Penugasan Guru',
            'lesson_hour_id' => 'Jam Pelajaran',
            'day_of_week' => 'Hari',
            'schedule_status' => 'Status Jadwal',
        ];
    }
}
