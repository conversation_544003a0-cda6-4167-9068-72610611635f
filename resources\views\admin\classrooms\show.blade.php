@extends('admin.layouts.app')

@section('title', 'Detail <PERSON>las - ' . $classroom->name)

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Detail Kelas - ' . $classroom->name,
        'breadcrumb' => 'Manajemen Akademik',
    ])

    <div class="row">
        <!-- Informasi Kelas -->
        <div class="col-lg-4">
            <div class="card ribbon-box border shadow-none mb-lg-0">
                <div class="card-header d-flex align-items-center">
                    <div class="ribbon ribbon-primary ribbon-shape"><span>Informasi Kelas</span></div>
                    @php
                        $color = $classroom->activity_status->color();
                        $label = $classroom->activity_status->label();
                    @endphp
                    <div class="ms-auto">
                        <span class="badge bg-{{ $color }}-subtle text-{{ $color }} fs-12">{{ $label }}</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-4">
                        <div class="flex-shrink-0">
                            <div class="avatar-lg rounded-circle bg-light text-primary">
                                <div class="avatar-title rounded-circle bg-primary-subtle text-primary fs-24">
                                    <i class="ri-community-line"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">{{ $classroom->name }}</h4>
                            <p class="text-muted mb-0">{{ $classroom->code }}</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <div class="p-2 border border-dashed rounded">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-2">
                                        <div class="avatar-title rounded bg-primary-subtle text-primary">
                                            <i class="ri-user-follow-line fs-18"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <p class="text-muted mb-0">Siswa</p>
                                        <h5 class="mb-0">
                                            <span class="counter-value" data-target="{{ $classroom->students()->count() }}">{{ $classroom->students()->count() }}</span>/{{ $classroom->capacity }}
                                        </h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-2 border border-dashed rounded">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-2">
                                        <div class="avatar-title rounded bg-primary-subtle text-primary">
                                            <i class="ri-scales-3-line fs-18"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <p class="text-muted mb-0">Tingkat</p>
                                        <h5 class="mb-0">{{ $classroom->level }}</h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5 class="fs-13 text-muted text-uppercase">Detail Kelas:</h5>
                        <div class="table-responsive">
                            <table class="table table-borderless table-sm mb-0">
                                <tbody>
                                    <tr>
                                        <th scope="row" class="fw-medium"><i class="ri-book-3-line text-primary me-2"></i>Program</th>
                                        <td>{{ $classroom->program->name ?? '-' }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row" class="fw-medium"><i class="ri-time-line text-primary me-2"></i>Shift</th>
                                        <td>{{ $classroom->shift->name ?? '-' }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row" class="fw-medium"><i class="ri-user-2-line text-primary me-2"></i>Wali Kelas</th>
                                        <td>{{ $classroom->homeroomTeacher->user->name ?? '-' }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row" class="fw-medium"><i class="ri-calendar-2-line text-primary me-2"></i>Tahun Akademik</th>
                                        <td>{{ $classroom->academicYear->name ?? '-' }}</td>
                                    </tr>
                                    @if ($classroom->description)
                                        <tr>
                                            <th scope="row" class="fw-medium"><i class="ri-file-list-3-line text-primary me-2"></i>Deskripsi</th>
                                            <td>{{ $classroom->description }}</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="d-flex flex-column gap-2">
                        <a href="{{ route('admin.classrooms.edit', $classroom->id) }}" class="btn btn-primary">
                            <i class="ri-pencil-fill align-bottom me-1"></i> Edit Kelas
                        </a>
                        <a href="{{ route('admin.classrooms.teacher-assignments', $classroom->id) }}" class="btn btn-info">
                            <i class="ri-user-settings-line align-bottom me-1"></i> Kelola Pengajar
                        </a>
                        <a href="{{ route('admin.classrooms.index') }}" class="btn btn-soft-secondary">
                            <i class="ri-arrow-left-line align-bottom me-1"></i> Kembali
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Daftar Siswa -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header border-0">
                    <div class="d-flex align-items-center">
                        <div>
                            <h5 class="card-title mb-0">Daftar Siswa</h5>
                            <p class="text-muted mb-0 mt-1">Mengelola siswa yang terdaftar di kelas ini</p>
                        </div>
                        <div class="flex-shrink-0 ms-auto">
                            <div class="d-flex gap-2 flex-wrap">
                                <div class="search-box">
                                    <input type="text" class="form-control search" id="search-student" placeholder="Cari siswa...">
                                    <i class="ri-search-line search-icon"></i>
                                </div>
                                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addStudentModal">
                                    <i class="ri-user-add-line align-bottom me-1"></i> Tambah Siswa
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body border-bottom border-bottom-dashed p-0">
                    <div class="d-flex justify-content-between align-items-center p-3">
                        <div class="flex-shrink-0">
                            <div class="text-muted">
                                <span class="fw-semibold" id="total-students">0</span> dari
                                <span class="fw-semibold">{{ $classroom->capacity }}</span> kapasitas kelas
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive table-card">
                        <table id="students-table" class="table align-middle table-nowrap mb-0" style="width: 100%;">
                            <thead class="table-light text-muted">
                                <tr>
                                    <th scope="col" style="width: 60px;">Nomor</th>
                                    <th>NISN</th>
                                    <th>Nama Siswa</th>
                                    <th>Jenis Kelamin</th>
                                    <th style="width: 100px;">Aksi</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Tambah Siswa -->
    <div class="modal fade" id="addStudentModal" tabindex="-1" aria-labelledby="addStudentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header p-3 bg-soft-primary">
                    <h5 class="modal-title" id="addStudentModalLabel">Tambah Siswa ke Kelas</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <form id="addStudentForm">
                    @csrf
                    <div class="modal-body p-4">
                        <div class="mb-4">
                            <label for="student_id" class="form-label">
                                Pilih Siswa <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="student_id" name="student_id[]" multiple required>
                                <option value="">Pilih Siswa</option>
                            </select>
                            <div class="form-text">
                                Siswa yang belum terdaftar di kelas untuk tahun akademik saat ini
                            </div>
                        </div>

                        <div class="alert alert-info border-0">
                            <div class="d-flex">
                                <div class="flex-shrink-0 me-3">
                                    <i class="ri-information-line fs-16 align-middle"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <span class="fw-medium">Informasi:</span> Hanya siswa yang belum terdaftar di kelas ini
                                    untuk tahun akademik {{ $classroom->academicYear->name }} yang ditampilkan.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <div class="hstack gap-2 justify-content-end">
                            <button type="button" class="btn btn-ghost-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-success" id="add-student-btn">
                                <i class="ri-user-add-line align-bottom me-1"></i> Tambah Siswa
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@include('admin.partials.plugins._jquery')
@include('admin.partials.plugins._datatables')

@push('scripts')
    <script>
        let studentChoices;

        $(document).ready(function() {
            // Initialize DataTable with improved settings
            var table = $('#students-table').DataTable({
                lengthChange: false,
                searching: false,
                ordering: false,
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('admin.classrooms.show', $classroom->id) }}",
                    data: function(d) {
                        d.search.value = $('#search-student').val();
                    }
                },
                columns: [{
                        data: 'sequence',
                        name: 'sequence'
                    },
                    {
                        data: 'student_nisn',
                        name: 'student.nisn'
                    },
                    {
                        data: 'student_name',
                        name: 'student.user.name',
                    },
                    {
                        data: 'student_gender',
                        name: 'student.gender',
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false,
                    }
                ],
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: "Cari...",
                    zeroRecords: "Data tidak ditemukan",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya",
                    }
                },
                drawCallback: function(settings) {
                    var api = this.api();
                    var recordsTotal = api.page.info().recordsTotal;
                    $('#total-students').text(recordsTotal);
                }
            });

            // Search functionality
            $('#search-student').on('keyup', function() {
                table.ajax.reload();
            });

            // Menyesuaikan kolom tabel saat sidebar di-toggle
            $('#topnav-hamburger-icon').click(() => {
                setTimeout(() => {
                    table.columns.adjust().draw();
                }, 300);
            });

            // Reset form when modal is hidden
            $('#addStudentModal').on('hidden.bs.modal', function() {
                $('#addStudentForm')[0].reset();
                $('#student_id').find('option:not(:first)').remove();
            });

            // Init Choices.js sekali
            const studentSelect = document.getElementById('student_id');
            // Init multi-select
            studentChoices = new Choices(studentSelect, {
                placeholder: true,
                placeholderValue: 'Pilih Siswa',
                searchEnabled: true,
                searchPlaceholderValue: "Cari siswa...",
                itemSelectText: "",
                noResultsText: "Tidak ada hasil",
                noChoicesText: "Tidak ada pilihan",
                shouldSort: false,
                removeItemButton: true // <- tombol hapus untuk setiap item terpilih
            });

            $('#addStudentModal').on('show.bs.modal', function() {
                studentChoices.clearChoices();
                studentChoices.setChoices([{
                    value: '',
                    label: 'Memuat data siswa...',
                    disabled: true
                }], 'value', 'label', false);
                studentChoices.disable();

                $.ajax({
                    url: "{{ route('admin.classrooms.available-students', $classroom->id) }}",
                    type: 'GET',
                    success: function(response) {
                        if (response.success) {
                            const formattedData = response.data.map(student => ({
                                value: student.id,
                                label: `${student.nis} - ${student.user.name}`
                            }));

                            studentChoices.clearChoices();
                            studentChoices.setChoices(formattedData, 'value', 'label', true);
                        } else {
                            studentChoices.clearChoices();
                            studentChoices.setChoices([{
                                value: '',
                                label: 'Gagal memuat data',
                                disabled: true
                            }], 'value', 'label', false);
                        }
                    },
                    error: function() {
                        studentChoices.clearChoices();
                        studentChoices.setChoices([{
                            value: '',
                            label: 'Terjadi kesalahan',
                            disabled: true
                        }], 'value', 'label', false);
                    },
                    complete: function() {
                        studentChoices.enable();
                    }
                });
            });

            $('#addStudentForm').on('submit', function(e) {
                e.preventDefault();

                const formData = $(this).serialize();
                const submitBtn = $('#add-student-btn');
                const originalText = submitBtn.html();

                submitBtn.prop('disabled', true)
                    .html('<span class="spinner-border spinner-border-sm me-1" role="status"></span> Menambahkan...');

                $.ajax({
                    url: "{{ route('admin.classrooms.add-student', $classroom->id) }}",
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            // Tutup modal
                            $('#addStudentModal').modal('hide');

                            // Reset form
                            $('#addStudentForm')[0].reset();

                            // Reset pilihan Choices.js (multi select)
                            if (typeof studentChoices !== 'undefined') {
                                studentChoices.removeActiveItems(); // hapus semua pilihan aktif
                            }

                            // Reload DataTable
                            table.ajax.reload();

                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonClass: 'btn btn-primary'
                            });
                        } else {
                            Swal.fire('Gagal!', response.message, 'error');
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON || JSON.parse(xhr.responseText);
                        Swal.fire({
                            title: 'Gagal!',
                            text: response.message || 'Terjadi kesalahan saat menambahkan siswa',
                            icon: 'error',
                            confirmButtonClass: 'btn btn-primary'
                        });
                    },
                    complete: function() {
                        submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });


            // Remove Student with enhanced confirmation and UI
            $('#students-table').on('click', '.btn-remove-student', function() {
                var studentId = $(this).data('student-id');
                var studentName = $(this).data('student-name');
                var row = $(this).closest('tr');

                Swal.fire({
                    title: 'Apakah Anda yakin?',
                    text: `Siswa "${studentName}" akan dihapus dari kelas ini!`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Ya, hapus!',
                    cancelButtonText: 'Batal',
                    confirmButtonClass: 'btn btn-danger',
                    cancelButtonClass: 'btn btn-light',
                    customClass: {
                        popup: 'swal2-border-radius'
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Add loading animation to the row
                        row.addClass('table-active');

                        $.ajax({
                            url: `/admin/classrooms/{{ $classroom->id }}/remove-student/${studentId}`,
                            type: 'DELETE',
                            data: {
                                _token: '{{ csrf_token() }}'
                            },
                            success: function(response) {
                                if (response.success) {
                                    row.fadeOut(400, function() {
                                        table.ajax.reload(function() {
                                            Swal.fire({
                                                title: 'Berhasil!',
                                                text: response.message,
                                                icon: 'success',
                                                confirmButtonClass: 'btn btn-primary'
                                            });
                                        });
                                    });
                                } else {
                                    row.removeClass('table-active');
                                    Swal.fire('Gagal!', response.message, 'error');
                                }
                            },
                            error: function(xhr) {
                                row.removeClass('table-active');
                                var response = xhr.responseJSON || JSON.parse(xhr.responseText);
                                Swal.fire('Gagal!', response.message || 'Terjadi kesalahan', 'error');
                            }
                        });
                    }
                });
            });
        });
    </script>
@endpush
