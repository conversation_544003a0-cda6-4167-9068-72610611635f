@extends('admin.layouts.app')

@section('title', 'Tambah Penugasan Guru')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Tambah Penugasan Guru',
        'breadcrumb' => 'Manajemen Mengajar',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">Tambah Penugasan Guru Baru</h5>
                    </div>
                </div>

                <div class="card-body">
                    <form id="create-form" method="POST" action="{{ route('admin.teacher_assignments.store') }}">
                        @csrf

                        <div class="row g-3">
                            <!-- Guru -->
                            <div class="col-md-6">
                                <label for="teacher_id" class="form-label">Guru <span class="text-danger">*</span></label>
                                <select class="form-select @error('teacher_id') is-invalid @enderror" data-choices id="teacher_id" name="teacher_id" required>
                                    <option value="" selected disabled>Pilih <PERSON></option>
                                    @foreach ($teachers as $teacher)
                                        <option value="{{ $teacher->id }}" {{ old('teacher_id') == $teacher->id ? 'selected' : '' }}>
                                            {{ $teacher->user->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('teacher_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Mata Pelajaran -->
                            <div class="col-md-6">
                                <label for="subject_id" class="form-label">Mata Pelajaran <span class="text-danger">*</span></label>
                                <select class="form-select @error('subject_id') is-invalid @enderror" data-choices id="subject_id" name="subject_id" required>
                                    <option value="" selected disabled>Pilih Mata Pelajaran</option>
                                    @foreach ($subjects as $subject)
                                        <option value="{{ $subject->id }}" {{ old('subject_id') == $subject->id ? 'selected' : '' }}>
                                            {{ $subject->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('subject_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Kelas -->
                            <div class="col-md-6">
                                <label for="classroom_id" class="form-label">Kelas <span class="text-danger">*</span></label>
                                <select class="form-select @error('classroom_id') is-invalid @enderror" data-choices id="classroom_id" name="classroom_id" required>
                                    <option value="" selected disabled>Pilih Kelas</option>
                                    @foreach ($classrooms as $classroom)
                                        <option value="{{ $classroom->id }}" {{ old('classroom_id') == $classroom->id ? 'selected' : '' }}>
                                            {{ $classroom->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('classroom_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Tahun Akademik -->
                            <div class="col-md-6">
                                <label for="academic_year_id" class="form-label">Tahun Akademik <span class="text-danger">*</span></label>
                                <select class="form-select @error('academic_year_id') is-invalid @enderror" data-choices id="academic_year_id" name="academic_year_id" required>
                                    <option value="" selected disabled>Pilih Tahun Akademik</option>
                                    @foreach ($academicYears as $academicYear)
                                        <option value="{{ $academicYear->id }}" {{ old('academic_year_id') == $academicYear->id ? 'selected' : '' }}>
                                            {{ $academicYear->name }} - {{ $academicYear->semester->label() }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('academic_year_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Tombol Aksi -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="hstack justify-content-end gap-2">
                                    <a href="{{ route('admin.teacher_assignments.index') }}" class="btn btn-ghost-secondary">
                                        <i class="ri-close-line align-bottom"></i> Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submit-btn">
                                        <i class="ri-save-line align-bottom"></i> Simpan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
