<?php

namespace App\Models;

use App\Enums\ScheduleStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Enums\DayOfWeekEnum;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ClassSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'teacher_assignment_id',
        'lesson_hour_id',
        'day_of_week',
        'schedule_status',
    ];

    protected $casts = [
        'day_of_week' => DayOfWeekEnum::class,
        'schedule_status' => ScheduleStatusEnum::class,
    ];

    public function teacherAssignment(): BelongsTo
    {
        return $this->belongsTo(TeacherAssignment::class);
    }

    public function lessonHour(): BelongsTo
    {
        return $this->belongsTo(LessonHour::class);
    }

    public function studentAttendances(): HasMany
    {
        return $this->hasMany(StudentAttendance::class);
    }

    public function teacherAttendances(): HasMany
    {
        return $this->hasMany(TeacherAttendance::class);
    }

    public function journals(): HasMany
    {
        return $this->hasMany(Journal::class);
    }

    public function substitutionRecords(): HasMany
    {
        return $this->hasMany(SubstitutionRecord::class);
    }
}
