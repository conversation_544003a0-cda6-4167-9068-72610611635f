@extends('admin.layouts.app')

@section('title', '<PERSON><PERSON><PERSON> Pelajaran')

@section('content')
    @include('admin.components.page-title', ['title' => 'Jadwal Pelajaran', 'breadcrumb' => '<PERSON>adwal Pelajaran'])

    <div class="row">
        <div class="col-lg-12">
            <div class="card" id="scheduleList">
                <!-- Start Header -->
                <div class="card-header border-0">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">Jadwal Pelajaran
                            <span class="badge bg-primary-subtle text-primary ms-2" id="total-schedules">0</span>
                        </h5>
                        <div class="flex-shrink-0">
                            <div class="d-flex gap-2">
                                <a href="{{ route('admin.class_schedules.create') }}" class="btn btn-primary">
                                    <i class="ri-add-line align-bottom me-1"></i> <PERSON><PERSON> J<PERSON>wal
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Header -->

                <!-- Start Filter Section -->
                <div class="card-body border border-dashed border-end-0 border-start-0">
                    <form id="schedule-form" class="row g-3 align-items-end" method="POST" action="{{ route('admin.class_schedules.view') }}">
                        @csrf
                        <div class="col-md-4">
                            <label for="classroom_id" class="form-label">Kelas</label>
                            <select class="form-select" data-choices name="classroom_id" id="classroom_id" required>
                                <option value="">Pilih Kelas</option>
                                @foreach ($classrooms as $classroom)
                                    <option value="{{ $classroom->id }}">{{ $classroom->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="academic_year_id" class="form-label">Tahun Akademik</label>
                            <select class="form-select" data-choices name="academic_year_id" id="academic_year_id" required>
                                <option value="">Pilih Tahun Akademik</option>
                                @foreach ($academicYears as $academicYear)
                                    <option value="{{ $academicYear->id }}">
                                        {{ $academicYear->name }} - {{ $academicYear->semester->label() }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4 d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary mt-2 mt-md-0 w-100">
                                <i class="ri-search-line align-bottom me-1"></i> Lihat Jadwal
                            </button>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->


                <!-- Start Table Section -->
                <div class="card-body">
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center">
                            <i class="ri-information-line me-2 fs-4"></i>
                            <div>
                                <h6 class="mb-1">Petunjuk Penggunaan</h6>
                                <p class="mb-0">Silahkan pilih kelas dan tahun akademik terlebih dahulu untuk melihat dan mengelola jadwal pelajaran.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Table Section -->
            </div>
            <!--end card-->
        </div>
        <!--end col-->
    </div>
    <!--end row-->
@endsection

@include('admin.partials.plugins._jquery')

@push('scripts')
    <script>
        $(document).ready(function() {
            // Menyesuaikan kolom tabel saat sidebar di-toggle
            $('#topnav-hamburger-icon').click(() => {
                setTimeout(() => {
                    if (typeof table !== 'undefined') {
                        table.columns.adjust().draw();
                    }
                }, 300);
            });
        });
    </script>
@endpush
