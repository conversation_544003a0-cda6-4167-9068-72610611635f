<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Admin\{
    AcademicYearController,
    AdminDashboardController,
    ClassroomController,
    ConfigurationController,
    JournalController,
    LessonHour<PERSON>ontroller,
    ProgramController,
    <PERSON><PERSON><PERSON><PERSON>roll<PERSON>,
    StudentController,
    SubjectController,
    TeacherAssignmentController,
    TeacherController,
    TeacherAttendanceController,
    StudentAttendanceController as AdminStudentAttendanceController,
    ClassScheduleController,
    LeaveRequestController,
    SubstitutionRecordController,
    UserController
};

// Redirect root to login
Route::redirect('/', 'login', 301);

// Auth routes
Auth::routes();

// Authenticated routes
Route::middleware(['auth'])->group(function () {

    Route::prefix('admin')
        ->name('admin.')
        ->middleware(['role:admin'])
        ->group(function () {

            /**
             * =====================================
             * DASHBOARD
             * =====================================
             */
            Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

            /**
             * =====================================
             * MASTER DATA
             * =====================================
             */
            Route::resource('academic_years', AcademicYearController::class);
            Route::resource('programs', ProgramController::class);
            Route::resource('subjects', SubjectController::class);
            Route::resource('shifts', ShiftController::class);
            Route::resource('lesson_hours', LessonHourController::class);
            Route::resource('configurations', ConfigurationController::class);

            /**
             * =====================================
             * CLASSROOM MANAGEMENT
             * =====================================
             */
            Route::resource('classrooms', ClassroomController::class)->except(['show']);
            Route::controller(ClassroomController::class)->group(function () {
                // Classroom teacher assignments
                Route::get('classrooms/{classroom}/teacher-assignments', 'classroomTeacherAssignments')
                    ->name('classrooms.teacher-assignments');
                Route::post('classrooms/{classroom}/teacher-assignments', 'storeTeacherAssignment')
                    ->name('classrooms.teacher-assignments.store');
                Route::put('classrooms/{classroom}/teacher-assignments/{assignment}', 'updateTeacherAssignment')
                    ->name('classrooms.teacher-assignments.update');
                Route::delete('classrooms/{classroom}/teacher-assignments/{assignment}', 'destroyTeacherAssignment')
                    ->name('classrooms.teacher-assignments.destroy');

                // Classroom details and student management
                Route::get('classrooms/{classroom}/show', 'show')
                    ->name('classrooms.show');
                Route::get('classrooms/{classroom}/available-students', 'availableStudents')
                    ->name('classrooms.available-students');
                Route::post('classrooms/{classroom}/add-student', 'addStudent')
                    ->name('classrooms.add-student');
                Route::delete('classrooms/{classroom}/remove-student/{student}', 'removeStudent')
                    ->name('classrooms.remove-student');
            });

            /**
             * =====================================
             * PEOPLE MANAGEMENT
             * =====================================
             */
            Route::resource('teachers', TeacherController::class);
            Route::resource('students', StudentController::class);
            Route::resource('teacher_assignments', TeacherAssignmentController::class);

            /**
             * =====================================
             * CLASS SCHEDULE MANAGEMENT
             * =====================================
             */
            Route::resource('class_schedules', ClassScheduleController::class);
            Route::post('class_schedules/view', [ClassScheduleController::class, 'viewSchedule'])
                ->name('class_schedules.view');

            /**
             * =====================================
             * API ROUTES FOR VUE.JS INTEGRATION
             * =====================================
             */
            Route::prefix('api')->name('api.')->group(function () {
                Route::get('class_schedules', [App\Http\Controllers\Api\ClassScheduleApiController::class, 'getSchedules'])
                    ->name('class_schedules.index');
                Route::post('class_schedules', [App\Http\Controllers\Api\ClassScheduleApiController::class, 'store'])
                    ->name('class_schedules.store');
                Route::put('class_schedules/{id}', [App\Http\Controllers\Api\ClassScheduleApiController::class, 'update'])
                    ->name('class_schedules.update');
                Route::delete('class_schedules/{id}', [App\Http\Controllers\Api\ClassScheduleApiController::class, 'destroy'])
                    ->name('class_schedules.destroy');
            });

            /**
             * =====================================
             * FUTURE / OPTIONAL FEATURES
             * =====================================
             */
            // Route::resource('leave_requests', LeaveRequestController::class);
            // Route::resource('substitution_records', SubstitutionRecordController::class);
            // Route::resource('users', UserController::class);
            // Route::get('teacher_attendances/reports', [TeacherAttendanceController::class, 'reports'])->name('admin.teacher_attendances.reports');
            // Route::get('student_attendances/reports', [AdminStudentAttendanceController::class, 'reports'])->name('admin.student_attendances.reports');
            // Route::get('journals/reports', [JournalController::class, 'reports'])->name('admin.journals.reports');
        });
});
