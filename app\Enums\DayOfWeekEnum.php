<?php

namespace App\Enums;

enum DayOfWeekEnum: string
{
    case Monday = 'monday';
    case Tuesday = 'tuesday';
    case Wednesday = 'wednesday';
    case Thursday = 'thursday';
    case Friday = 'friday';
    case Saturday = 'saturday';
    case Sunday = 'sunday';

    public function label(): string
    {
        return match ($this) {
            self::Monday => 'Senin',
            self::Tuesday => 'Selasa',
            self::Wednesday => 'Rabu',
            self::Thursday => 'Kamis',
            self::Friday => 'Jumat',
            self::Saturday => 'Sabtu',
            self::Sunday => 'Minggu',
        };
    }

    public static function options(): array
    {
        return collect(self::cases())->mapWithKeys(fn($case) => [
            $case->value => $case->label()
        ])->toArray();
    }

    public function color(): string
    {
        return match ($this) {
            self::Monday => 'primary',
            self::Tuesday => 'secondary',
            self::Wednesday => 'success',
            self::Thursday => 'info',
            self::Friday => 'warning',
            self::Saturday => 'danger',
            self::Sunday => 'light',
        };
    }
}
