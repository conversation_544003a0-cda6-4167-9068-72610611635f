@extends('admin.layouts.app')

@section('title', 'Detail Akademik Kelas')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Detail Akademik Kelas',
        'breadcrumb' => 'Manajemen Sekolah',
        'breadcrumb_items' => [
            [
                'text' => 'Kelas',
                'url' => route('admin.classrooms.index'),
            ],
            [
                'text' => 'Detail Akademik',
                'url' => null,
            ],
        ],
    ])

    <div id="academic-details-app">
        <!-- Classroom Info Card -->
        <div class="row mb-4">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar-lg">
                                    <div class="avatar-title bg-primary-subtle text-primary rounded-circle">
                                        <i class="ri-building-4-line fs-24"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-1">{{ $classroom->name }}</h5>
                                <p class="text-muted mb-2">{{ $classroom->academicYear->name }}</p>
                                <div class="hstack gap-3 text-muted">
                                    <span><i class="ri-user-star-line me-1"></i>{{ $classroom->teacher->user->name ?? 'Belum ditugaskan' }}</span>
                                    <span><i class="ri-group-line me-1"></i>{{ $classroom->students_count ?? 0 }} Siswa</span>
                                    <span><i class="ri-book-line me-1"></i>{{ $classroom->program->name ?? 'N/A' }}</span>
                                    <span><i class="ri-time-line me-1"></i>{{ $classroom->shift?->name ?? 'N/A' }}</span>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <div class="hstack gap-2">
                                    <button class="btn btn-ghost-primary" @click="refreshData" :disabled="loading">
                                        <i class="ri-refresh-line align-bottom"></i> Refresh
                                    </button>
                                    <button class="btn btn-ghost-info" @click="printPage">
                                        <i class="ri-printer-line align-bottom"></i> Cetak
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <!-- Card Header -->
                    <div class="card-header border-bottom-dashed">
                        <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                            <!-- Title with Counter -->
                            <div>
                                <h5 class="card-title mb-0 d-flex align-items-center">
                                    Manajemen Akademik Kelas
                                </h5>
                                <p class="text-muted mb-0 mt-1">Kelola penugasan guru dan jadwal pelajaran</p>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- Tabs for Teacher Assignments and Schedule -->
                        <ul class="nav nav-pills nav-justified mb-4" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link" :class="{ active: activeTab === 'assignments' }" @click="activeTab = 'assignments'" href="#assignments" data-bs-toggle="tab" role="tab">
                                    <i class="ri-user-star-line me-2"></i> Penugasan Guru
                                    <span class="badge bg-primary ms-2">@{{ teacherAssignments.length }}</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" :class="{ active: activeTab === 'schedule' }" @click="activeTab = 'schedule'" href="#schedule" data-bs-toggle="tab" role="tab">
                                    <i class="ri-calendar-schedule-line me-2"></i> Jadwal Pelajaran
                                    <span class="badge bg-success ms-2">@{{ scheduleCount }}</span>
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content">
                            <!-- Teacher Assignments Tab -->
                            <div class="tab-pane" :class="{ active: activeTab === 'assignments' }" id="assignments" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">Daftar Penugasan Guru</h6>
                                    <button class="btn btn-primary btn-sm" @click="openTeacherModal('create')" :disabled="loading">
                                        <i class="ri-add-line me-1"></i> Tambah Guru
                                    </button>
                                </div>
                                <div class="table-responsive" v-if="teacherAssignments.length > 0">
                                    <table class="table table-nowrap align-middle mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th class="text-center" width="5%">#</th>
                                                <th>Guru</th>
                                                <th>Mata Pelajaran</th>
                                                <th class="text-center">Status</th>
                                                <th class="text-center" width="15%">Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(assignment, index) in teacherAssignments" :key="assignment.id">
                                                <td class="text-center">@{{ index + 1 }}</td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-xs me-3">
                                                            <div class="avatar-title bg-primary-subtle text-primary rounded-circle">
                                                                @{{ assignment.teacher.user.name[0] }}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-0">@{{ assignment.teacher.user.name }}</h6>
                                                            <small class="text-muted">@{{ assignment.teacher.employee_id || 'N/A' }}</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info-subtle text-info">@{{ assignment.subject?.name || 'N/A' }}</span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge" :class="assignment.is_homeroom_teacher ? 'bg-success' : 'bg-primary'">
                                                        @{{ assignment.is_homeroom_teacher ? 'Wali Kelas' : 'Guru Mapel' }}
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <div class="btn-group">
                                                        <button class="btn btn-sm btn-soft-primary" @click="editTeacherAssignment(assignment)" title="Edit">
                                                            <i class="ri-pencil-line"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-soft-danger" @click="confirmDeleteTeacherAssignment(assignment)" title="Hapus">
                                                            <i class="ri-delete-bin-line"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div v-else class="text-center py-4">
                                    <div class="avatar-lg mx-auto mb-3">
                                        <div class="avatar-title bg-light text-muted rounded-circle">
                                            <i class="ri-user-star-line fs-1"></i>
                                        </div>
                                    </div>
                                    <h6>Belum ada guru yang ditugaskan</h6>
                                    <p class="text-muted">Klik tombol "Tambah Guru" untuk menugaskan guru ke kelas ini</p>
                                </div>
                            </div>

                            <!-- Schedule Tab -->
                            <div class="tab-pane" :class="{ active: activeTab === 'schedule' }" id="schedule" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">Jadwal Pelajaran Mingguan</h6>
                                    <button class="btn btn-success btn-sm" @click="openScheduleModal('create')" :disabled="loading || !lessonHours.length">
                                        <i class="ri-calendar-schedule-line me-1"></i> Tambah Jadwal
                                    </button>
                                </div>
                                <div v-if="loading" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p class="mt-2">Memuat jadwal...</p>
                                </div>
                                <div v-else-if="!lessonHours.length" class="text-center py-4">
                                    <div class="avatar-lg mx-auto mb-3">
                                        <div class="avatar-title bg-light text-muted rounded-circle">
                                            <i class="ri-calendar-schedule-line fs-1"></i>
                                        </div>
                                    </div>
                                    <h6>Belum ada jam pelajaran</h6>
                                    <p class="text-muted">Tambahkan jam pelajaran terlebih dahulu untuk membuat jadwal.</p>
                                    <a :href="'/admin/lesson-hours/create'" class="btn btn-primary btn-sm">
                                        <i class="ri-add-line me-1"></i> Tambah Jam Pelajaran
                                    </a>
                                </div>
                                <div v-else class="table-responsive">
                                    <table class="table table-bordered table-sm align-middle mb-0">
                                        <thead class="table-light text-center">
                                            <tr>
                                                <th width="12%">Jam</th>
                                                <th v-for="(dayName, dayKey) in days" :key="dayKey">@{{ dayName }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="hour in lessonHours" :key="hour.id">
                                                <td class="text-center fw-medium bg-light">
                                                    <div class="small fw-semibold">@{{ hour.name }}</div>
                                                    <div class="text-muted" style="font-size: 11px;">
                                                        @{{ hour.formatted_start_time }} - @{{ hour.formatted_end_time }}
                                                    </div>
                                                </td>
                                                <td v-for="(dayName, dayKey) in days" :key="`${hour.id}-${dayKey}`"
                                                    class="position-relative schedule-cell"
                                                    :class="getSchedule(hour.id, dayKey) ? 'p-1 has-schedule' : 'text-center schedule-empty-cell'"
                                                    style="min-height: 60px; vertical-align: middle;"
                                                    :data-hour-id="hour.id" :data-day-key="dayKey"
                                                    @drop="onDrop($event, hour.id, dayKey)" @dragover.prevent @dragenter.prevent>
                                                    <div v-if="getSchedule(hour.id, dayKey)" class="schedule-item p-2 rounded position-relative h-100"
                                                         :style="getScheduleStyle(hour.id, dayKey)" :draggable="true"
                                                         :data-schedule-id="getSchedule(hour.id, dayKey).id"
                                                         @dragstart="onDragStart($event, getSchedule(hour.id, dayKey))"
                                                         @click="editSchedule(getSchedule(hour.id, dayKey))">
                                                        <div class="fw-semibold small">
                                                            @{{ getAssignment(getSchedule(hour.id, dayKey).teacher_assignment_id)?.subject?.name || 'N/A' }}
                                                        </div>
                                                        <div class="text-muted" style="font-size: 11px;">
                                                            @{{ getAssignment(getSchedule(hour.id, dayKey).teacher_assignment_id)?.teacher?.user?.name || 'N/A' }}
                                                        </div>
                                                        <div class="schedule-actions position-absolute" style="top: 2px; right: 2px; display: none;">
                                                            <div class="btn-group">
                                                                <button class="btn btn-xs btn-primary" @click.stop="editSchedule(getSchedule(hour.id, dayKey))">
                                                                    <i class="ri-pencil-line"></i>
                                                                </button>
                                                                <button class="btn btn-xs btn-danger" @click.stop="confirmDeleteSchedule(getSchedule(hour.id, dayKey))">
                                                                    <i class="ri-delete-bin-line"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <button v-else class="btn btn-sm btn-ghost-primary add-schedule-btn" style="display: none;"
                                                            @click="openScheduleModal('create', hour.id, dayKey)">
                                                        <i class="ri-add-circle-line"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Teacher Assignment Modal -->
        <div class="modal fade" id="teacherAssignmentModal" tabindex="-1" aria-labelledby="teacherAssignmentModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="teacherAssignmentModalLabel">
                            <i class="ri-user-star-line me-2"></i> @{{ teacherModalTitle }}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form @submit.prevent="submitTeacherForm" class="needs-validation" novalidate>
                        <div class="modal-body">
                            <div class="alert alert-info border-0" role="alert">
                                <div class="d-flex">
                                    <i class="ri-information-line fs-16 me-2"></i>
                                    <div>
                                        <h6 class="alert-heading mb-1">Informasi Penugasan</h6>
                                        <p class="mb-0">@{{ teacherModalInfo }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">Guru <span class="text-danger">*</span></label>
                                    <select class="form-select" v-model="teacherForm.teacher_id" :class="{ 'is-invalid': teacherErrors.teacher_id }" required>
                                        <option value="">Pilih Guru</option>
                                        <option v-for="teacher in teachers" :key="teacher.id" :value="teacher.id">
                                            @{{ teacher.user.name }}
                                        </option>
                                    </select>
                                    <div v-if="teacherErrors.teacher_id" class="invalid-feedback">@{{ teacherErrors.teacher_id }}</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Mata Pelajaran <span class="text-danger">*</span></label>
                                    <select class="form-select" v-model="teacherForm.subject_id" :class="{ 'is-invalid': teacherErrors.subject_id }" required>
                                        <option value="">Pilih Mata Pelajaran</option>
                                        <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
                                            @{{ subject.name }}
                                        </option>
                                    </select>
                                    <div v-if="teacherErrors.subject_id" class="invalid-feedback">@{{ teacherErrors.subject_id }}</div>
                                </div>
                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" v-model="teacherForm.is_homeroom_teacher" id="is_homeroom_teacher">
                                        <label class="form-check-label" for="is_homeroom_teacher">Jadikan sebagai Wali Kelas</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-ghost-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-primary" :disabled="submitting">
                                <i class="ri-save-line me-1"></i>
                                <span v-if="submitting">Menyimpan...</span>
                                <span v-else>Simpan</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Schedule Modal -->
        <div class="modal fade" id="scheduleModal" tabindex="-1" aria-labelledby="scheduleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="scheduleModalLabel">
                            <i class="ri-calendar-schedule-line me-2"></i> @{{ scheduleModalTitle }}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form @submit.prevent="submitScheduleForm" class="needs-validation" novalidate>
                        <div class="modal-body">
                            <div class="alert alert-info border-0" role="alert">
                                <div class="d-flex">
                                    <i class="ri-information-line fs-16 me-2"></i>
                                    <div>
                                        <h6 class="alert-heading mb-1">Informasi Jadwal</h6>
                                        <p class="mb-0">@{{ scheduleModalInfo }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">Guru & Mata Pelajaran <span class="text-danger">*</span></label>
                                    <select class="form-select" v-model="scheduleForm.teacher_assignment_id" :class="{ 'is-invalid': scheduleErrors.teacher_assignment_id }" required>
                                        <option value="">Pilih Guru & Mata Pelajaran</option>
                                        <option v-for="assignment in teacherAssignments" :key="assignment.id" :value="assignment.id">
                                            @{{ assignment.teacher.user.name }} - @{{ assignment.subject?.name || 'N/A' }}@{{ assignment.is_homeroom_teacher ? ' (Wali Kelas)' : '' }}
                                        </option>
                                    </select>
                                    <div v-if="scheduleErrors.teacher_assignment_id" class="invalid-feedback">@{{ scheduleErrors.teacher_assignment_id }}</div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Jam Pelajaran <span class="text-danger">*</span></label>
                                    <select class="form-select" v-model="scheduleForm.lesson_hour_id" :class="{ 'is-invalid': scheduleErrors.lesson_hour_id }" required>
                                        <option value="">Pilih Jam</option>
                                        <option v-for="hour in lessonHours" :key="hour.id" :value="hour.id">
                                            @{{ hour.name }} (@{{ hour.formatted_start_time }} - @{{ hour.formatted_end_time }})
                                        </option>
                                    </select>
                                    <div v-if="scheduleErrors.lesson_hour_id" class="invalid-feedback">@{{ scheduleErrors.lesson_hour_id }}</div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Hari <span class="text-danger">*</span></label>
                                    <select class="form-select" v-model="scheduleForm.day_of_week" :class="{ 'is-invalid': scheduleErrors.day_of_week }" required>
                                        <option value="">Pilih Hari</option>
                                        <option v-for="(dayName, dayKey) in days" :key="dayKey" :value="dayKey">
                                            @{{ dayName }}
                                        </option>
                                    </select>
                                    <div v-if="scheduleErrors.day_of_week" class="invalid-feedback">@{{ scheduleErrors.day_of_week }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-ghost-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-success" :disabled="submitting">
                                <i class="ri-save-line me-1"></i>
                                <span v-if="submitting">Menyimpan...</span>
                                <span v-else>Simpan</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        .nav-pills .nav-link {
            border-radius: 0.375rem;
            margin: 0 0.25rem;
            padding: 0.5rem 1rem;
            color: #6c757d;
            font-weight: 500;
            transition: all 0.15s ease-in-out;
        }

        .nav-pills .nav-link.active {
            background-color: var(--vz-primary);
            color: white;
        }

        .nav-pills .nav-link:hover:not(.active) {
            background-color: #f8f9fa;
            color: var(--vz-primary);
        }

        .schedule-cell {
            transition: background-color 0.15s ease;
        }

        .schedule-empty-cell:hover {
            background-color: rgba(var(--vz-primary-rgb), 0.05);
        }

        .schedule-empty-cell:hover .add-schedule-btn {
            display: inline-block !important;
        }

        .schedule-item {
            transition: all 0.15s ease;
            cursor: move;
            min-height: 50px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .schedule-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .schedule-item:hover .schedule-actions {
            display: block !important;
        }

        .schedule-actions .btn {
            padding: 0.125rem 0.25rem;
            font-size: 0.6875rem;
        }

        .schedule-item.dragging {
            opacity: 0.5;
            border: 2px dashed var(--vz-primary);
        }

        .schedule-empty-cell.drop-target {
            background-color: rgba(var(--vz-success-rgb), 0.1);
            border: 2px dashed var(--vz-success);
        }

        @media print {

            .btn,
            .schedule-actions,
            .add-schedule-btn,
            .card-header {
                display: none !important;
            }

            .page-content {
                margin-left: 0 !important;
                padding: 0 !important;
            }

            .card {
                box-shadow: none !important;
                border: none !important;
            }
        }
    </style>
@endpush

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/interactjs@1.10.27/dist/interact.min.js"></script>

    <script>
        const {
            createApp
        } = Vue;

        createApp({
            data() {
                return {
                    classroom: @json($classroom),
                    teacherAssignments: @json($teacherAssignments),
                    schedules: @json($schedules),
                    days: @json($days),
                    lessonHours: @json($lessonHours),
                    teachers: [],
                    subjects: [],
                    activeTab: 'assignments',
                    loading: false,
                    submitting: false,
                    teacherModalMode: 'create',
                    scheduleModalMode: 'create',
                    currentTeacherAssignment: null,
                    currentSchedule: null,
                    teacherForm: {
                        teacher_id: '',
                        subject_id: '',
                        is_homeroom_teacher: false
                    },
                    scheduleForm: {
                        teacher_assignment_id: '',
                        lesson_hour_id: '',
                        day_of_week: ''
                    },
                    teacherErrors: {},
                    scheduleErrors: {},
                    draggedSchedule: null,
                    subjectColors: {},
                    colorPalette: [
                        "#3498db", "#2ecc71", "#9b59b6", "#e74c3c", "#f39c12",
                        "#1abc9c", "#d35400", "#34495e", "#16a085", "#27ae60",
                        "#8e44ad", "#f1c40f", "#e67e22", "#c0392b", "#7f8c8d"
                    ]
                };
            },

            computed: {
                teacherModalTitle() {
                    return this.teacherModalMode === 'create' ? 'Tambah Penugasan Guru' : 'Edit Penugasan Guru';
                },
                scheduleModalTitle() {
                    return this.scheduleModalMode === 'create' ? 'Tambah Jadwal Pelajaran' : 'Edit Jadwal Pelajaran';
                },
                teacherModalInfo() {
                    const action = this.teacherModalMode === 'create' ? 'Menambahkan' : 'Mengedit';
                    return `${action} penugasan guru untuk kelas ${this.classroom.name} pada tahun akademik ${this.classroom.academic_year.name}.`;
                },
                scheduleModalInfo() {
                    const action = this.scheduleModalMode === 'create' ? 'Menambahkan' : 'Mengedit';
                    return `${action} jadwal untuk kelas ${this.classroom.name} pada tahun akademik ${this.classroom.academic_year.name}.`;
                },
                scheduleCount() {
                    return Object.values(this.schedules).reduce((count, hour) => {
                        return count + Object.values(hour.days).filter(schedule => schedule !== null).length;
                    }, 0);
                }
            },

            mounted() {
                this.loadTeachers();
                this.loadSubjects();
                this.initializeFormValidation();
                this.generateSubjectColors();
            },

            methods: {
                initializeFormValidation() {
                    const forms = document.querySelectorAll('.needs-validation');
                    Array.from(forms).forEach(form => {
                        form.addEventListener('submit', event => {
                            if (!form.checkValidity()) {
                                event.preventDefault();
                                event.stopPropagation();
                            }
                            form.classList.add('was-validated');
                        }, false);
                    });
                },

                async loadTeachers() {
                    try {
                        const response = await fetch('/admin/ajax/teachers', {
                            headers: {
                                'Accept': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            }
                        });
                        const data = await response.json();
                        this.teachers = data.data || [];
                        if (!this.teachers.length) {
                            this.showAlert('Tidak ada data guru tersedia', 'warning');
                        }
                    } catch (error) {
                        this.showAlert('Gagal memuat data guru: ' + error.message, 'error');
                    }
                },

                async loadSubjects() {
                    const programId = this.classroom.program_id || 0;
                    try {
                        const response = await fetch(`/admin/subjects/data?program_id=${programId}`, {
                            headers: {
                                'Accept': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            }
                        });
                        const data = await response.json();
                        this.subjects = data.data || [];
                        if (!this.subjects.length) {
                            this.showAlert('Tidak ada mata pelajaran tersedia', 'warning');
                        }
                    } catch (error) {
                        this.showAlert('Gagal memuat mata pelajaran: ' + error.message, 'error');
                    }
                },

                generateSubjectColors() {
                    this.subjectColors = {};
                    let colorIndex = 0;
                    this.teacherAssignments.forEach(assignment => {
                        if (assignment.subject && !this.subjectColors[assignment.subject.id]) {
                            this.subjectColors[assignment.subject.id] = this.colorPalette[colorIndex % this.colorPalette.length];
                            colorIndex++;
                        }
                    });
                },

                openTeacherModal(mode, assignment = null) {
                    this.teacherModalMode = mode;
                    this.currentTeacherAssignment = assignment;
                    this.teacherForm = {
                        teacher_id: assignment ? assignment.teacher_id : '',
                        subject_id: assignment ? assignment.subject_id : '',
                        is_homeroom_teacher: assignment ? assignment.is_homeroom_teacher : false
                    };
                    this.teacherErrors = {};
                    const modal = new bootstrap.Modal(document.getElementById('teacherAssignmentModal'));
                    modal.show();
                },

                openScheduleModal(mode, lessonHourId = null, dayOfWeek = null, schedule = null) {
                    this.scheduleModalMode = mode;
                    this.currentSchedule = schedule;
                    this.scheduleForm = {
                        teacher_assignment_id: schedule ? schedule.teacher_assignment_id : '',
                        lesson_hour_id: schedule ? schedule.lesson_hour_id : lessonHourId || '',
                        day_of_week: schedule ? schedule.day_of_week : dayOfWeek || ''
                    };
                    this.scheduleErrors = {};
                    const modal = new bootstrap.Modal(document.getElementById('scheduleModal'));
                    modal.show();
                },

                async submitTeacherForm() {
                    const form = document.querySelector('#teacherAssignmentModal form');
                    if (!form.checkValidity()) {
                        form.classList.add('was-validated');
                        return;
                    }

                    this.submitting = true;
                    this.teacherErrors = {};

                    try {
                        const formData = new FormData();
                        formData.append('teacher_id', this.teacherForm.teacher_id);
                        formData.append('subject_id', this.teacherForm.subject_id);
                        formData.append('is_homeroom_teacher', this.teacherForm.is_homeroom_teacher ? 1 : 0);
                        formData.append('classroom_id', this.classroom.id);
                        formData.append('academic_year_id', this.classroom.academic_year_id);

                        let url = '/admin/teacher-assignments';
                        if (this.teacherModalMode === 'edit' && this.currentTeacherAssignment) {
                            url = `/admin/teacher-assignments/${this.currentTeacherAssignment.id}`;
                            formData.append('_method', 'PUT');
                        }

                        const response = await fetch(url, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                'Accept': 'application/json'
                            },
                            body: formData
                        });

                        const data = await response.json();
                        if (data.success) {
                            this.showAlert(data.message || 'Penugasan guru berhasil disimpan', 'success');
                            bootstrap.Modal.getInstance(document.getElementById('teacherAssignmentModal')).hide();
                            await this.refreshData();
                        } else {
                            this.teacherErrors = data.errors || {};
                            throw new Error(data.message || 'Gagal menyimpan penugasan');
                        }
                    } catch (error) {
                        this.showAlert(error.message, 'error');
                    } finally {
                        this.submitting = false;
                    }
                },

                async submitScheduleForm() {
                    const form = document.querySelector('#scheduleModal form');
                    if (!form.checkValidity()) {
                        form.classList.add('was-validated');
                        return;
                    }

                    this.submitting = true;
                    this.scheduleErrors = {};

                    try {
                        const formData = new FormData();
                        formData.append('teacher_assignment_id', this.scheduleForm.teacher_assignment_id);
                        formData.append('lesson_hour_id', this.scheduleForm.lesson_hour_id);
                        formData.append('day_of_week', this.scheduleForm.day_of_week);
                        formData.append('classroom_id', this.classroom.id);
                        formData.append('academic_year_id', this.classroom.academic_year_id);

                        let url = '/admin/class-schedules';
                        if (this.scheduleModalMode === 'edit' && this.currentSchedule) {
                            url = `/admin/class-schedules/${this.currentSchedule.id}`;
                            formData.append('_method', 'PUT');
                        }

                        const response = await fetch(url, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                'Accept': 'application/json'
                            },
                            body: formData
                        });

                        const data = await response.json();
                        if (data.success) {
                            this.showAlert(data.message || 'Jadwal berhasil disimpan', 'success');
                            bootstrap.Modal.getInstance(document.getElementById('scheduleModal')).hide();
                            await this.refreshData();
                        } else {
                            this.scheduleErrors = data.errors || {};
                            throw new Error(data.message || 'Gagal menyimpan jadwal');
                        }
                    } catch (error) {
                        this.showAlert(error.message, 'error');
                    } finally {
                        this.submitting = false;
                    }
                },

                confirmDeleteTeacherAssignment(assignment) {
                    Swal.fire({
                        title: 'Konfirmasi Hapus',
                        text: 'Penugasan guru ini akan dihapus permanen. Lanjutkan?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Ya, Hapus!',
                        cancelButtonText: 'Batal',
                        confirmButtonClass: 'btn btn-primary w-xs me-2',
                        cancelButtonClass: 'btn btn-danger w-xs',
                        buttonsStyling: false
                    }).then(result => {
                        if (result.isConfirmed) {
                            this.deleteTeacherAssignment(assignment);
                        }
                    });
                },

                async deleteTeacherAssignment(assignment) {
                    try {
                        const response = await fetch(`/admin/teacher-assignments/${assignment.id}`, {
                            method: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                'Accept': 'application/json'
                            }
                        });
                        const data = await response.json();
                        if (data.success) {
                            this.showAlert(data.message || 'Penugasan guru berhasil dihapus', 'success');
                            await this.refreshData();
                        } else {
                            throw new Error(data.message || 'Gagal menghapus penugasan');
                        }
                    } catch (error) {
                        this.showAlert(error.message, 'error');
                    }
                },

                confirmDeleteSchedule(schedule) {
                    Swal.fire({
                        title: 'Konfirmasi Hapus',
                        text: 'Jadwal ini akan dihapus permanen. Lanjutkan?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Ya, Hapus!',
                        cancelButtonText: 'Batal',
                        confirmButtonClass: 'btn btn-primary w-xs me-2',
                        cancelButtonClass: 'btn btn-danger w-xs',
                        buttonsStyling: false
                    }).then(result => {
                        if (result.isConfirmed) {
                            this.deleteSchedule(schedule);
                        }
                    });
                },

                async deleteSchedule(schedule) {
                    try {
                        const response = await fetch(`/admin/class-schedules/${schedule.id}`, {
                            method: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                'Accept': 'application/json'
                            }
                        });
                        const data = await response.json();
                        if (data.success) {
                            this.showAlert(data.message || 'Jadwal berhasil dihapus', 'success');
                            await this.refreshData();
                        } else {
                            throw new Error(data.message || 'Gagal menghapus jadwal');
                        }
                    } catch (error) {
                        this.showAlert(error.message, 'error');
                    }
                },

                getSchedule(lessonHourId, dayOfWeek) {
                    return this.schedules[lessonHourId]?.days[dayOfWeek] || null;
                },

                getAssignment(assignmentId) {
                    return this.teacherAssignments.find(a => a.id === assignmentId) || null;
                },

                getScheduleStyle(lessonHourId, dayOfWeek) {
                    const schedule = this.getSchedule(lessonHourId, dayOfWeek);
                    if (!schedule) return {};
                    const assignment = this.getAssignment(schedule.teacher_assignment_id);
                    const color = assignment?.subject ? this.subjectColors[assignment.subject.id] : '#6c757d';
                    return {
                        borderLeft: `3px solid ${color}`,
                        backgroundColor: `${color}20`
                    };
                },

                onDragStart(event, schedule) {
                    this.draggedSchedule = schedule;
                    event.dataTransfer.setData('text/plain', schedule.id);
                    event.target.classList.add('dragging');
                },

                async onDrop(event, lessonHourId, dayOfWeek) {
                    event.preventDefault();
                    if (!this.draggedSchedule) return;

                    if (this.draggedSchedule.lesson_hour_id === lessonHourId && this.draggedSchedule.day_of_week === dayOfWeek) {
                        this.showAlert('Jadwal tidak dapat dipindahkan ke posisi yang sama', 'warning');
                        return;
                    }

                    if (this.getSchedule(lessonHourId, dayOfWeek)) {
                        this.showAlert('Slot ini sudah terisi. Pilih slot kosong atau edit jadwal.', 'warning');
                        return;
                    }

                    this.loading = true;
                    try {
                        const formData = new FormData();
                        formData.append('teacher_assignment_id', this.draggedSchedule.teacher_assignment_id);
                        formData.append('lesson_hour_id', lessonHourId);
                        formData.append('day_of_week', dayOfWeek);
                        formData.append('classroom_id', this.classroom.id);
                        formData.append('academic_year_id', this.classroom.academic_year_id);
                        formData.append('_method', 'PUT');

                        const response = await fetch(`/admin/class-schedules/${this.draggedSchedule.id}`, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                'Accept': 'application/json'
                            },
                            body: formData
                        });

                        const data = await response.json();
                        if (data.success) {
                            this.showAlert('Jadwal berhasil dipindahkan', 'success');
                            await this.refreshData();
                        } else {
                            throw new Error(data.message || 'Gagal memindahkan jadwal');
                        }
                    } catch (error) {
                        this.showAlert(error.message, 'error');
                    } finally {
                        this.loading = false;
                        this.draggedSchedule = null;
                        document.querySelectorAll('.dragging').forEach(el => el.classList.remove('dragging'));
                    }
                },

                async refreshData() {
                    this.loading = true;
                    try {
                        const response = await fetch(`/admin/classrooms/${this.classroom.id}/academic-details`, {
                            headers: {
                                'Accept': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            }
                        });
                        const data = await response.json();
                        if (data.success) {
                            this.classroom = data.classroom;
                            this.teacherAssignments = data.teacherAssignments;
                            this.schedules = data.schedules;
                            this.lessonHours = data.lessonHours;
                            this.days = data.days;
                            this.generateSubjectColors();
                        } else {
                            throw new Error(data.message || 'Gagal memuat data');
                        }
                    } catch (error) {
                        this.showAlert('Gagal memuat data: ' + error.message, 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                printPage() {
                    window.print();
                },

                showAlert(message, type = 'info') {
                    Swal.fire({
                        title: type === 'success' ? 'Berhasil!' : type === 'error' ? 'Error!' : 'Perhatian!',
                        text: message,
                        icon: type,
                        confirmButtonClass: 'btn btn-primary w-xs',
                        buttonsStyling: false
                    });
                },

                editTeacherAssignment(assignment) {
                    this.openTeacherModal('edit', assignment);
                },

                editSchedule(schedule) {
                    this.openScheduleModal('edit', null, null, schedule);
                }
            }
        }).mount('#academic-details-app');
    </script>
@endpush
