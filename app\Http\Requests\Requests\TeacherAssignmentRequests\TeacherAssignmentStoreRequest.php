<?php

namespace App\Http\Requests\Requests\TeacherAssignmentRequests;

use Illuminate\Foundation\Http\FormRequest;

class TeacherAssignmentStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'teacher_id' => ['required', 'exists:teachers,id'],
            'subject_id' => ['required', 'exists:subjects,id'],
            'classroom_id' => ['required', 'exists:classrooms,id'],
            'academic_year_id' => ['required', 'exists:academic_years,id'],
        ];
    }

    public function messages(): array
    {
        return [
            'teacher_id.required' => 'Guru harus dipilih',
            'teacher_id.exists' => 'Guru yang dipilih tidak valid',
            'subject_id.required' => 'Mata pelajaran harus dipilih',
            'subject_id.exists' => 'Mata pelajaran yang dipilih tidak valid',
            'classroom_id.required' => 'Kelas harus dipilih',
            'classroom_id.exists' => 'Kelas yang dipilih tidak valid',
            'academic_year_id.required' => 'Tahun akademik harus dipilih',
            'academic_year_id.exists' => 'Tahun akademik yang dipilih tidak valid',
        ];
    }
}
