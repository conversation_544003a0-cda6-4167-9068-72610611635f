<?php

namespace App\Http\Controllers\Admin;

use App\Models\Classroom;
use App\Models\Program;
use App\Models\Shift;
use App\Models\Teacher;
use App\Models\AcademicYear;
use App\Models\Student;
use App\Models\ClassroomStudent;
use App\Enums\ActivityStatusEnum;
use App\Models\TeacherAssignment;
use App\Models\Subject;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Requests\Requests\ClassroomRequests\ClassroomStoreRequest;
use App\Http\Requests\Requests\ClassroomRequests\ClassroomUpdateRequest;

class ClassroomController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->datatables($request);
        }

        return view('admin.classrooms.index', [
            'programs' => Program::where('activity_status', ActivityStatusEnum::Active)->get(),
            'shifts' => Shift::where('activity_status', ActivityStatusEnum::Active)->get(),
            'academicYears' => AcademicYear::all(),
            'statuses' => ActivityStatusEnum::options(),
        ]);
    }

    public function datatables($req)
    {
        $query = Classroom::with(['program', 'shift', 'homeroomTeacher.user', 'academicYear']);

        // Filter berdasarkan program
        if ($req->filled('program_id')) {
            $query->where('program_id', $req->program_id);
        }

        // Filter berdasarkan shift
        if ($req->filled('shift_id')) {
            $query->where('shift_id', $req->shift_id);
        }

        // Filter berdasarkan tahun akademik
        if ($req->filled('academic_year_id')) {
            $query->where('academic_year_id', $req->academic_year_id);
        }

        // Filter berdasarkan status
        if ($req->filled('activity_status')) {
            $query->where('activity_status', $req->activity_status);
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('action', function ($row) {
                return view('admin.classrooms._action', compact('row'));
            })
            ->editColumn('activity_status', function ($row) {
                $color = $row->activity_status->color();
                $label = $row->activity_status->label();
                return "<span class='badge bg-{$color}-subtle text-{$color}'>{$label}</span>";
            })
            ->editColumn('program_name', function ($row) {
                return $row->program->name ?? '-';
            })
            ->editColumn('shift_name', function ($row) {
                return $row->shift->name ?? '-';
            })
            ->editColumn('homeroom_teacher', function ($row) {
                return $row->homeroomTeacher->user->name ?? '-';
            })
            ->editColumn('academic_year', function ($row) {
                $format = $row->academicYear->name . ' - ' . $row->academicYear->semester->label(); //format: 2024/2025 - Ganjil/Genap
                return $format ?? '-';
            })
            ->editColumn('capacity', function ($row) {
                $currentCount = ClassroomStudent::where([
                    'classroom_id' => $row->id,
                    'academic_year_id' => $row->academic_year_id,
                ])->count();

                $maxCapacity = $row->capacity;
                $percentage = $maxCapacity > 0 ? ($currentCount / $maxCapacity) * 100 : 0;

                $colorClass = 'success';
                if ($percentage >= 90) {
                    $colorClass = 'danger';
                } elseif ($percentage >= 75) {
                    $colorClass = 'warning';
                }

                return "<span class='badge bg-{$colorClass}-subtle text-{$colorClass}'>{$currentCount}/{$maxCapacity}</span>";
            })
            // Search global untuk semua kolom
            ->filter(function ($q) use ($req) {
                if ($search = $req->input('search')) {
                    $q->where(function ($sub) use ($search) {
                        $sub->where('code', 'like', "%{$search}%")
                            ->orWhere('name', 'like', "%{$search}%")
                            ->orWhere('level', 'like', "%{$search}%")
                            ->orWhereHas('program', function ($query) use ($search) {
                                $query->where('name', 'like', "%{$search}%");
                            })
                            ->orWhereHas('homeroomTeacher.user', function ($query) use ($search) {
                                $query->where('name', 'like', "%{$search}%");
                            })
                            ->orWhereHas('academicYear', function ($query) use ($search) {
                                $query->where('name', 'like', "%{$search}%");
                            });
                    });
                }
            })
            ->rawColumns(['action', 'activity_status', 'capacity'])
            ->toJson();
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.classrooms.create', [
            'programs' => Program::where('activity_status', ActivityStatusEnum::Active)->get(),
            'shifts' => Shift::where('activity_status', ActivityStatusEnum::Active)->get(),
            //tampilkan teacher yang belum ada di kelas lain
            'teachers' => Teacher::with('user')->whereDoesntHave('homeroomClassrooms')->get(),
            'academicYears' => AcademicYear::all(),
            'statuses' => ActivityStatusEnum::options(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ClassroomStoreRequest $request)
    {
        // Mengambil data yang sudah divalidasi dari request.
        $data = $request->validated();

        // Menggunakan transaction untuk memastikan operasi berhasil atau tidak sama sekali.
        DB::transaction(function () use ($data) {
            // Buat kelas baru.
            Classroom::create($data);
        });

        return redirect()->route('admin.classrooms.index')
            ->with('success', 'Kelas berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id, Request $request)
    {
        $classroom = Classroom::with(['program', 'shift', 'homeroomTeacher.user', 'academicYear'])->findOrFail($id);

        if ($request->ajax()) {
            return $this->studentsDataTable($classroom, $request);
        }

        return view('admin.classrooms.show', compact('classroom'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        return view('admin.classrooms.edit', [
            'programs' => Program::where('activity_status', ActivityStatusEnum::Active)->get(),
            'shifts' => Shift::where('activity_status', ActivityStatusEnum::Active)->get(),
            //tampilkan teacher yang belum ada di kelas lain
            'teachers' => Teacher::with('user')->whereDoesntHave('homeroomClassrooms')->get(),
            'academicYears' => AcademicYear::all(),
            'statuses' => ActivityStatusEnum::options(),
            'classroom' => Classroom::findOrFail($id),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ClassroomUpdateRequest $request, string $id)
    {
        // Mengambil data yang sudah divalidasi dari request.
        $data = $request->validated();

        // Mencari data kelas berdasarkan ID.
        $classroom = Classroom::findOrFail($id);

        // Menggunakan transaction untuk memastikan operasi berhasil atau tidak sama sekali.
        DB::transaction(function () use ($classroom, $data) {
            // Update data kelas.
            $classroom->update($data);
        });

        return redirect()->route('admin.classrooms.index')
            ->with('success', 'Kelas berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $classroom = Classroom::findOrFail($id);

            // Cek apakah kelas memiliki relasi yang tidak boleh dihapus
            if ($classroom->students()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kelas tidak dapat dihapus karena masih memiliki siswa.'
                ], 400);
            }

            if ($classroom->teacherAssignments()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kelas tidak dapat dihapus karena masih memiliki penugasan guru.'
                ], 400);
            }

            $classroom->delete();

            return response()->json([
                'success' => true,
                'message' => 'Kelas berhasil dihapus.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus kelas.'
            ], 500);
        }
    }

    /**
     * DataTable untuk students di classroom
     */
    private function studentsDataTable(Classroom $classroom, Request $req)
    {
        $query = ClassroomStudent::with(['student.user'])
            ->where('classroom_id', $classroom->id)
            ->where('academic_year_id', $classroom->academic_year_id)
            ->orderBy('sequence');

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('student_nisn', function ($row) {
                return $row->student->nisn ?? '-';
            })
            ->addColumn('student_name', function ($row) {
                return $row->student->user->name ?? '-';
            })
            ->addColumn('student_gender', function ($row) {
                $gender = $row->student->gender ?? null;
                if ($gender) {
                    $color = $gender->color();
                    return "<span class='badge bg-{$color}-subtle text-{$color}'>{$gender->label()}</span>";
                }
                return '-';
            })
            ->addColumn('action', function ($row) {
                return view('admin.classrooms._student_action', compact('row'));
            })
            ->filter(function ($q) use ($req) {
                if ($search = $req->input('search.value')) {
                    $q->where(function ($sub) use ($search) {
                        $sub->whereHas('student.user', function ($q2) use ($search) {
                            $q2->where('name', 'like', "%{$search}%");
                        })
                            ->orWhereHas('student', function ($q3) use ($search) {
                                $q3->where('nisn', 'like', "%{$search}%");
                            });
                    });
                }
            })
            ->rawColumns(['student_gender', 'action'])
            ->toJson();
    }

    public function availableStudents(string $classroomId)
    {
        $classroom = Classroom::findOrFail($classroomId);
        // Get students yang belum ada di kelas manapun untuk pada tahun akademik akif
        $availableStudents = Student::with('user')
            ->whereDoesntHave('classrooms', function ($query) use ($classroom) {
                $query->where('classrooms.academic_year_id', $classroom->academic_year_id);
            })
            ->get();

        return response()->json([
            'success' => true,
            'data' => $availableStudents
        ]);
    }

    public function addStudent(Request $request, string $id)
    {
        $request->validate([
            'student_id' => 'required|array',
            'student_id.*' => 'exists:students,id',
        ]);

        $classroom = Classroom::findOrFail($id);

        $added = [];
        $skipped = [];

        foreach ($request->student_id as $studentId) {
            // Cek apakah sudah ada di kelas ini pada tahun akademik yang sama
            $exists = ClassroomStudent::where([
                'classroom_id' => $classroom->id,
                'student_id' => $studentId,
                'academic_year_id' => $classroom->academic_year_id,
            ])->exists();

            if ($exists) {
                $skipped[] = [
                    'id' => $studentId,
                    'message' => 'Siswa sudah ada di kelas ini'
                ];
                continue;
            }

            // Cek kapasitas kelas
            $currentCount = ClassroomStudent::where([
                'classroom_id' => $classroom->id,
                'academic_year_id' => $classroom->academic_year_id,
            ])->count();

            if ($currentCount >= $classroom->capacity) {
                $skipped[] = [
                    'id' => $studentId,
                    'message' => 'Kapasitas kelas penuh'
                ];
                continue;
            }

            try {
                $nextSequence = ClassroomStudent::where([
                    'classroom_id' => $classroom->id,
                    'academic_year_id' => $classroom->academic_year_id,
                ])->max('sequence') + 1;

                ClassroomStudent::create([
                    'classroom_id' => $classroom->id,
                    'student_id' => $studentId,
                    'academic_year_id' => $classroom->academic_year_id,
                    'sequence' => $nextSequence,
                ]);

                $added[] = $studentId;
            } catch (\Exception $e) {
                $skipped[] = [
                    'id' => $studentId,
                    'message' => 'Gagal menambahkan siswa'
                ];
            }
        }

        return response()->json([
            'success' => true,
            'message' => count($added) . ' siswa berhasil ditambahkan, ' . count($skipped) . ' dilewati.',
            'added' => $added,
            'skipped' => $skipped
        ]);
    }


    /**
     * Hapus student dari classroom
     */
    public function removeStudent(string $classroomId, string $studentId)
    {
        try {
            $classroom = Classroom::findOrFail($classroomId);

            $classroomStudent = ClassroomStudent::where([
                'classroom_id' => $classroom->id,
                'student_id' => $studentId,
                'academic_year_id' => $classroom->academic_year_id,
            ])->first();

            if (!$classroomStudent) {
                return response()->json([
                    'success' => false,
                    'message' => 'Siswa tidak ditemukan di kelas ini.'
                ], 404);
            }

            $classroomStudent->delete();

            // Reorder sequence numbers
            $this->reorderSequence($classroom->id, $classroom->academic_year_id);

            return response()->json([
                'success' => true,
                'message' => 'Siswa berhasil dihapus dari kelas.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus siswa.'
            ], 500);
        }
    }

    /**
     * Reorder sequence numbers after deletion
     */
    private function reorderSequence($classroomId, $academicYearId)
    {
        $students = ClassroomStudent::where([
            'classroom_id' => $classroomId,
            'academic_year_id' => $academicYearId,
        ])->orderBy('sequence')->get();

        foreach ($students as $index => $student) {
            $student->update(['sequence' => $index + 1]);
        }
    }

    public function classroomTeacherAssignments(string $classroomId, Request $request)
    {
        $classroom = Classroom::with(['program', 'shift', 'homeroomTeacher.user', 'academicYear'])->findOrFail($classroomId);

        if ($request->ajax()) {
            return $this->teacherAssignmentsDataTable($classroom, $request);
        }

        return view('admin.classrooms.teacher_assignments', [
            'classroom' => $classroom,
            'teachers' => Teacher::with('user')->get(),
            'subjects' => Subject::where('activity_status', ActivityStatusEnum::Active)
                ->where(function ($query) use ($classroom) {
                    // If classroom has a program, filter subjects by that program
                    if ($classroom->program_id) {
                        $query->where('program_id', $classroom->program_id)
                            ->orWhereNull('program_id');
                    }
                })
                ->get(),
        ]);
    }

    /**
     * DataTable for teacher assignments in a classroom
     */
    private function teacherAssignmentsDataTable(Classroom $classroom, Request $req)
    {
        $query = TeacherAssignment::with(['teacher.user', 'subject'])
            ->where('classroom_id', $classroom->id)
            ->where('academic_year_id', $classroom->academic_year_id);

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('teacher_name', function ($row) {
                return $row->teacher->user->name ?? '-';
            })
            ->addColumn('subject_name', function ($row) {
                return $row->subject->name ?? '-';
            })
            ->addColumn('action', function ($row) use ($classroom) {
                return view('admin.classrooms._teacher_assignment_action', [
                    'row' => $row,
                    'classroom' => $classroom
                ]);
            })
            ->filter(function ($q) use ($req) {
                if ($search = $req->input('search.value')) {
                    $q->where(function ($sub) use ($search) {
                        $sub->whereHas('teacher.user', function ($query) use ($search) {
                            $query->where('name', 'like', "%{$search}%");
                        })
                            ->orWhereHas('subject', function ($query) use ($search) {
                                $query->where('name', 'like', "%{$search}%");
                            });
                    });
                }
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    /**
     * Store a new teacher assignment
     */
    public function storeTeacherAssignment(Request $request, string $classroomId)
    {
        $request->validate([
            'teacher_id' => 'required|exists:teachers,id',
            'subject_id' => 'required|exists:subjects,id',
        ]);

        try {
            $classroom = Classroom::findOrFail($classroomId);

            // Check if the assignment already exists
            $exists = TeacherAssignment::where([
                'teacher_id' => $request->teacher_id,
                'subject_id' => $request->subject_id,
                'classroom_id' => $classroom->id,
                'academic_year_id' => $classroom->academic_year_id,
            ])->exists();

            if ($exists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Penugasan guru untuk mata pelajaran ini sudah ada di kelas ini.'
                ], 422);
            }

            // Create the assignment
            TeacherAssignment::create([
                'teacher_id' => $request->teacher_id,
                'subject_id' => $request->subject_id,
                'classroom_id' => $classroom->id,
                'academic_year_id' => $classroom->academic_year_id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Penugasan guru berhasil ditambahkan.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menambahkan penugasan guru.'
            ], 500);
        }
    }

    /**
     * Update a teacher assignment
     */
    public function updateTeacherAssignment(Request $request, string $classroomId, string $assignmentId)
    {
        $request->validate([
            'teacher_id' => 'required|exists:teachers,id',
            'subject_id' => 'required|exists:subjects,id',
        ]);

        try {
            $classroom = Classroom::findOrFail($classroomId);
            $assignment = TeacherAssignment::findOrFail($assignmentId);

            // Check if this is the same classroom
            if ($assignment->classroom_id != $classroom->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Penugasan guru tidak ditemukan di kelas ini.'
                ], 404);
            }

            // Check if the assignment already exists (except this one)
            $exists = TeacherAssignment::where([
                'teacher_id' => $request->teacher_id,
                'subject_id' => $request->subject_id,
                'classroom_id' => $classroom->id,
                'academic_year_id' => $classroom->academic_year_id,
            ])
                ->where('id', '!=', $assignmentId)
                ->exists();

            if ($exists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Penugasan guru untuk mata pelajaran ini sudah ada di kelas ini.'
                ], 422);
            }

            // Update the assignment
            $assignment->update([
                'teacher_id' => $request->teacher_id,
                'subject_id' => $request->subject_id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Penugasan guru berhasil diperbarui.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui penugasan guru.'
            ], 500);
        }
    }

    /**
     * Delete a teacher assignment
     */
    public function destroyTeacherAssignment(string $classroomId, string $assignmentId)
    {
        try {
            $classroom = Classroom::findOrFail($classroomId);
            $assignment = TeacherAssignment::findOrFail($assignmentId);

            // Check if this is the same classroom
            if ($assignment->classroom_id != $classroom->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Penugasan guru tidak ditemukan di kelas ini.'
                ], 404);
            }

            // Check if there are class schedules linked to this assignment
            if ($assignment->classSchedules()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Penugasan guru tidak dapat dihapus karena masih memiliki jadwal kelas.'
                ], 422);
            }

            // Delete the assignment
            $assignment->delete();

            return response()->json([
                'success' => true,
                'message' => 'Penugasan guru berhasil dihapus.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus penugasan guru.'
            ], 500);
        }
    }
}
