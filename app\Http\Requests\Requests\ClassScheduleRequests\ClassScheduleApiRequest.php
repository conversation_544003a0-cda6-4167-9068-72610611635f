<?php

namespace App\Http\Requests\Requests\ClassScheduleRequests;

use App\Enums\DayOfWeekEnum;
use App\Enums\ScheduleStatusEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class ClassScheduleApiRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'teacher_assignment_id' => ['required', 'exists:teacher_assignments,id'],
            'lesson_hour_id' => ['required', 'exists:lesson_hours,id'],
            'day_of_week' => ['required', new Enum(DayOfWeekEnum::class)],
            'schedule_status' => ['sometimes', new Enum(ScheduleStatusEnum::class)],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'teacher_assignment_id' => 'Penugasan Guru',
            'lesson_hour_id' => 'Jam Pelajaran',
            'day_of_week' => 'Hari',
            'schedule_status' => 'Status Jadwal',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'teacher_assignment_id.required' => 'Penugasan guru harus dipilih',
            'teacher_assignment_id.exists' => 'Penugasan guru tidak valid',
            'lesson_hour_id.required' => 'Jam pelajaran harus dipilih',
            'lesson_hour_id.exists' => 'Jam pelajaran tidak valid',
            'day_of_week.required' => 'Hari harus dipilih',
            'schedule_status.required' => 'Status jadwal harus dipilih',
        ];
    }
}
